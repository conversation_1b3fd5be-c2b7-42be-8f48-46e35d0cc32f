{"name": "app-scraper", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.9.0", "cheerio": "^1.0.0", "daisyui": "^5.0.35", "google-play-scraper": "^10.0.1", "googleapis": "^128.0.0", "lucide-react": "^0.510.0", "next": "^15.3.2", "next-themes": "^0.4.6", "node-html-parser": "^7.0.1", "react": "^19.1.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/axios": "^0.14.4", "@types/cheerio": "^1.0.0", "@types/jquery": "^3.5.32", "@types/node": "^20.17.50", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}