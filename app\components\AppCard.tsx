'use client';

import Image from 'next/image';
import { Star, Smartphone, Download } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

interface AppCardProps {
    app: {
        appId: string;
        appName: string;
        developer: string;
        rating: number | null;
        imgSrc: string;
        appUrl: string;
        installs?: number;
        category: string;
    };
}

export default function AppCard({ app }: AppCardProps) {
    const { appName, developer, rating, imgSrc, appId, installs, category } = app;
    const [imageError, setImageError] = useState(false);

    const formatInstalls = (count?: number) => {
        if (!count) return '';
        if (count >= 1000000000) return `${(count / 1000000000).toFixed(1)}B+`;
        if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M+`;
        if (count >= 1000) return `${(count / 1000).toFixed(1)}K+`;
        return `${count}+`;
    };

    return (
        <Link href={`/app/${appId}`} className="block">
            <div className="h-full bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col">
                <div className="p-4 flex gap-4">
                    <div className="relative w-16 h-16 flex-shrink-0">
                        {!imageError ? (
                            <Image
                                src={imgSrc}
                                alt={appName}
                                fill
                                className="rounded-xl object-cover"
                                onError={() => setImageError(true)}
                            />
                        ) : (
                            <div className="w-full h-full rounded-xl bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                <Smartphone className="w-8 h-8 text-gray-400" />
                            </div>
                        )}
                    </div>

                    <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2">
                            {appName && appName !== '.' && !appName.match(/^\d+(\.\d+)?star$/i)
                                ? appName
                                : 'Unknown App'}
                        </h3>

                        <div className="flex items-center gap-2 mt-1">
                            {category && !category.match(/^\d+(\.\d+)?star$/i) && (
                                <>
                                    <span className="text-sm text-gray-600 dark:text-gray-300 line-clamp-1">
                                        {category}
                                    </span>
                                    <span className="text-gray-400">•</span>
                                </>
                            )}
                            <span className="text-sm text-gray-600 dark:text-gray-300 line-clamp-1">
                                {developer && !developer.match(/^\d+(\.\d+)?star$/i)
                                    ? developer
                                    : 'Unknown Developer'}
                            </span>
                        </div>

                        <div className="flex items-center gap-3 mt-2">
                            {rating !== null && !isNaN(rating) && rating > 0 && rating <= 5 && (
                                <div className="flex items-center">
                                    <Star size={16} className="text-yellow-400 mr-1" fill="currentColor" />
                                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        {rating.toFixed(1)}
                                    </span>
                                </div>
                            )}
                            {installs !== undefined && (
                                <div className="flex items-center">
                                    <Download size={16} className="text-gray-400 mr-1" />
                                    <span className="text-sm text-gray-600 dark:text-gray-400">
                                        {formatInstalls(installs)}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="mt-auto p-3 bg-gray-50 dark:bg-gray-900">
                    <button className="w-full py-2 bg-primary text-white rounded-md text-sm hover:bg-primary/90 transition-colors">
                        View Details
                    </button>
                </div>
            </div>
        </Link>
    );
} 