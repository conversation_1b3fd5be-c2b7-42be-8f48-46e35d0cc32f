import { NextResponse } from 'next/server';
const gplayModule = require('google-play-scraper');
const gplay = gplayModule.default || gplayModule;

interface AppData {
    appName: string;
    developer: string;
    rating: number | null;
    imgSrc: string;
    appId: string;
    appUrl: string;
    installs: number;
    category: string;
}



export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '200');
    const categoryParam = searchParams.get('category') || 'ALL';

    // Get fetch limit parameter (how many apps to fetch from Google Play)
    const fetchLimit = Math.min(
        parseInt(searchParams.get('fetchLimit') || '500'),
        500 // Maximum limit to prevent overloading
    );

    // Get filter parameters
    const minInstalls = parseInt(searchParams.get('minInstalls') || '0');
    const maxInstalls = parseInt(searchParams.get('maxInstalls') || '0');
    const minRating = parseFloat(searchParams.get('minRating') || '0');
    const maxRating = parseFloat(searchParams.get('maxRating') || '5');

    console.log('Starting scrape with params:', { query, page, limit, categoryParam, fetchLimit });

    try {
        let apps: AppData[] = [];

        // Handle search based on query and/or category
        if (query) {
            // Search by query
            const searchResults = await gplay.search({
                term: query,
                num: fetchLimit,
                fullDetail: true,
                price: 'all'
            });

            apps = searchResults.map((app: any) => ({
                appName: app.title,
                developer: app.developer,
                rating: app.score || null,
                imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                appId: app.appId,
                appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                installs: parseInt(app.installs?.replace(/[^0-9]/g, '') || '0'),
                category: app.genreId?.toUpperCase() || 'UNKNOWN'
            }));
        } else {
            // Search by category only
            let categoryId;
            if (categoryParam === 'ALL') {
                categoryId = gplay.category.GAME;
            } else {
                // Map our category names to gplay category constants
                const categoryMap: { [key: string]: string } = {
                    'GAME': gplay.category.GAME,
                    'SOCIAL': gplay.category.SOCIAL,
                    'COMMUNICATION': gplay.category.COMMUNICATION,
                    'PRODUCTIVITY': gplay.category.PRODUCTIVITY,
                    'EDUCATION': gplay.category.EDUCATION,
                    'ENTERTAINMENT': gplay.category.ENTERTAINMENT,
                    'MUSIC_AND_AUDIO': gplay.category.MUSIC_AND_AUDIO,
                    'VIDEO_PLAYERS': gplay.category.VIDEO_PLAYERS,
                    'PHOTOGRAPHY': gplay.category.PHOTOGRAPHY,
                    'TOOLS': gplay.category.TOOLS,
                    'SHOPPING': gplay.category.SHOPPING,
                    'FINANCE': gplay.category.FINANCE,
                    'HEALTH_AND_FITNESS': gplay.category.HEALTH_AND_FITNESS,
                    'LIFESTYLE': gplay.category.LIFESTYLE,
                    'BOOKS_AND_REFERENCE': gplay.category.BOOKS_AND_REFERENCE
                };
                categoryId = categoryMap[categoryParam] || gplay.category.GAME;
            }

            console.log('Fetching apps for category:', categoryId);

            const categoryApps = await gplay.list({
                category: categoryId,
                collection: gplay.collection.TOP_FREE,
                num: fetchLimit,
                fullDetail: true,
                country: 'us',
                language: 'en'
            });

            apps = categoryApps.map((app: any) => ({
                appName: app.title,
                developer: app.developer,
                rating: app.score || null,
                imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                appId: app.appId,
                appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                installs: parseInt(app.installs?.replace(/[^0-9]/g, '') || '0'),
                category: app.genreId?.toUpperCase() || categoryParam
            }));
        }

        // Apply filters
        const filteredApps = apps.filter(app => {
            const installsValid = (!minInstalls || app.installs >= minInstalls) &&
                (!maxInstalls || app.installs <= maxInstalls);
            const ratingValid = (!minRating || (app.rating !== null && app.rating >= minRating)) &&
                (!maxRating || (app.rating !== null && app.rating <= maxRating));

            // Apply category filter if specified and not searching ALL
            // For category filtering, be more lenient since the API already filtered by category
            const categoryValid = categoryParam === 'ALL' ||
                app.category === categoryParam ||
                app.category.includes(categoryParam) ||
                categoryParam.includes('GAME'); // Special case for games

            return installsValid && ratingValid && categoryValid;
        });

        console.log(`Filtered ${apps.length} apps down to ${filteredApps.length} apps`);

        // Apply pagination
        const startIndex = (page - 1) * limit;
        const paginatedApps = filteredApps.slice(startIndex, startIndex + limit);
        const totalApps = filteredApps.length;
        const totalPages = Math.ceil(totalApps / limit);

        return NextResponse.json({
            apps: paginatedApps,
            total: totalApps,
            page,
            limit,
            totalPages,
            hasMore: page < totalPages,
            debug: {
                category: categoryParam,
                totalAppsBefore: apps.length,
                totalAppsAfter: paginatedApps.length,
                filters: { minInstalls, maxInstalls, minRating, maxRating }
            }
        });

    } catch (error: any) {
        console.error('Error scraping Google Play Store:', error);
        return NextResponse.json({
            error: 'Failed to fetch apps',
            details: error.message || 'Unknown error',
            category: categoryParam
        }, { status: 500 });
    }
}