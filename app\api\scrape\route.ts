import { NextResponse } from 'next/server';
import { formatCategoryName } from '../../constants/categories';
const gplayModule = require('google-play-scraper');
const gplay = gplayModule.default || gplayModule;

interface AppData {
    appName: string;
    developer: string;
    rating: number | null;
    imgSrc: string;
    appId: string;
    appUrl: string;
    installs: number;
    category: string;
}



export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '200');
    const categoryParam = searchParams.get('category') || 'ALL';

    // Get fetch limit parameter (how many apps to fetch from Google Play)
    // Note: Google Play Scraper has different limits for search vs category listing
    const requestedFetchLimit = parseInt(searchParams.get('fetchLimit') || '500');
    const fetchLimit = query
        ? Math.min(requestedFetchLimit, 500) // Search queries max 250
        : Math.min(requestedFetchLimit, 500); // Category listing max 500

    // Get filter parameters
    const minInstalls = parseInt(searchParams.get('minInstalls') || '0');
    const maxInstalls = searchParams.get('maxInstalls') ? parseInt(searchParams.get('maxInstalls')!) : null;
    const minRating = parseFloat(searchParams.get('minRating') || '0');
    const maxRating = searchParams.get('maxRating') ? parseFloat(searchParams.get('maxRating')!) : null;

    console.log('🔥 FIXED FILTERS v2 - Starting scrape with params:', { query, page, limit, categoryParam, fetchLimit, minInstalls, maxInstalls, minRating, maxRating });

    try {
        let apps: AppData[] = [];

        // Handle search based on query and/or category
        if (query) {
            // Search by query
            const searchResults = await gplay.search({
                term: query,
                num: fetchLimit,
                fullDetail: true,
                price: 'all'
            });

            apps = searchResults.map((app: any) => {
                // Extract actual category from the app data
                const actualCategory = app.genreId || app.genre || app.category || 'UNKNOWN';

                return {
                    appName: app.title,
                    developer: app.developer,
                    rating: app.score || null,
                    imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                    appId: app.appId,
                    appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                    installs: parseInt(app.installs?.replace(/[^0-9]/g, '') || '0'),
                    category: formatCategoryName(actualCategory)
                };
            });
        } else {
            // Search by category only
            let categoryId;
            if (categoryParam === 'ALL') {
                categoryId = gplay.category.GAME;
            } else {
                // Map our category names to gplay category constants
                const categoryMap: { [key: string]: string } = {
                    'GAME': gplay.category.GAME,
                    'SOCIAL': gplay.category.SOCIAL,
                    'COMMUNICATION': gplay.category.COMMUNICATION,
                    'PRODUCTIVITY': gplay.category.PRODUCTIVITY,
                    'EDUCATION': gplay.category.EDUCATION,
                    'ENTERTAINMENT': gplay.category.ENTERTAINMENT,
                    'MUSIC_AND_AUDIO': gplay.category.MUSIC_AND_AUDIO,
                    'VIDEO_PLAYERS': gplay.category.VIDEO_PLAYERS,
                    'PHOTOGRAPHY': gplay.category.PHOTOGRAPHY,
                    'TOOLS': gplay.category.TOOLS,
                    'SHOPPING': gplay.category.SHOPPING,
                    'FINANCE': gplay.category.FINANCE,
                    'HEALTH_AND_FITNESS': gplay.category.HEALTH_AND_FITNESS,
                    'LIFESTYLE': gplay.category.LIFESTYLE,
                    'BOOKS_AND_REFERENCE': gplay.category.BOOKS_AND_REFERENCE,
                    // Additional categories
                    'SPORTS': gplay.category.SPORTS,
                    'MEDICAL': gplay.category.MEDICAL,
                    'MAPS_AND_NAVIGATION': gplay.category.MAPS_AND_NAVIGATION,
                    'AUTO_AND_VEHICLES': gplay.category.AUTO_AND_VEHICLES,
                    'PERSONALIZATION': gplay.category.PERSONALIZATION,
                    'DATING': gplay.category.DATING,
                    'FOOD_AND_DRINK': gplay.category.FOOD_AND_DRINK,
                    'HOUSE_AND_HOME': gplay.category.HOUSE_AND_HOME,
                    // Previously removed categories that are actually available
                    'NEWS_AND_MAGAZINES': gplay.category.NEWS_AND_MAGAZINES,
                    'WEATHER': gplay.category.WEATHER,
                    'TRAVEL_AND_LOCAL': gplay.category.TRAVEL_AND_LOCAL,
                    'BUSINESS': gplay.category.BUSINESS,
                    // Additional Google Play categories
                    'ART_AND_DESIGN': gplay.category.ART_AND_DESIGN,
                    'BEAUTY': gplay.category.BEAUTY,
                    'COMICS': gplay.category.COMICS,
                    'EVENTS': gplay.category.EVENTS,
                    'PARENTING': gplay.category.PARENTING
                };
                categoryId = categoryMap[categoryParam] || gplay.category.GAME;
            }

            console.log('Fetching apps for category:', categoryParam, 'mapped to:', categoryId);

            let categoryApps;
            try {
                categoryApps = await gplay.list({
                    category: categoryId,
                    collection: gplay.collection.TOP_FREE,
                    num: fetchLimit,
                    fullDetail: true,
                    country: 'us',
                    language: 'en'
                });
                console.log(`Successfully fetched ${categoryApps.length} apps for category ${categoryParam}`);
            } catch (error) {
                console.error(`Error fetching category ${categoryParam}:`, error);
                // Fallback to games if category fails
                categoryApps = await gplay.list({
                    category: gplay.category.GAME,
                    collection: gplay.collection.TOP_FREE,
                    num: fetchLimit,
                    fullDetail: true,
                    country: 'us',
                    language: 'en'
                });
                console.log(`Fallback: fetched ${categoryApps.length} games instead`);
            }

            apps = categoryApps.map((app: any) => {
                // Extract actual category from the app data
                const actualCategory = app.genreId || app.genre || app.category || categoryParam;

                return {
                    appName: app.title,
                    developer: app.developer,
                    rating: app.score || null,
                    imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                    appId: app.appId,
                    appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                    installs: parseInt(app.installs?.replace(/[^0-9]/g, '') || '0'),
                    category: formatCategoryName(actualCategory)
                };
            });
        }

        // Apply filters
        const filteredApps = apps.filter(app => {
            const installsValid = (!minInstalls || app.installs >= minInstalls) &&
                (maxInstalls === null || app.installs <= maxInstalls);
            const ratingValid = (!minRating || (app.rating !== null && app.rating >= minRating)) &&
                (maxRating === null || (app.rating !== null && app.rating <= maxRating));

            // Apply category filter if specified and not searching ALL
            // When fetching by category, the API already filters by category, so we should be more lenient
            const categoryValid = categoryParam === 'ALL' ||
                !query; // If no search query, we're browsing by category so accept all apps from that category

            return installsValid && ratingValid && categoryValid;
        });

        console.log(`Filtered ${apps.length} apps down to ${filteredApps.length} apps`);

        // Apply pagination
        const startIndex = (page - 1) * limit;
        const paginatedApps = filteredApps.slice(startIndex, startIndex + limit);
        const totalApps = filteredApps.length;
        const totalPages = Math.ceil(totalApps / limit);

        return NextResponse.json({
            apps: paginatedApps,
            total: totalApps,
            page,
            limit,
            totalPages,
            hasMore: page < totalPages,
            debug: {
                category: categoryParam,
                totalAppsBefore: apps.length,
                totalAppsAfter: paginatedApps.length,
                filters: { minInstalls, maxInstalls, minRating, maxRating }
            }
        });

    } catch (error: any) {
        console.error('Error scraping Google Play Store:', error);
        return NextResponse.json({
            error: 'Failed to fetch apps',
            details: error.message || 'Unknown error',
            category: categoryParam
        }, { status: 500 });
    }
}