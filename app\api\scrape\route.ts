import { NextResponse } from 'next/server';
import { formatCategoryName } from '../../constants/categories';
const gplayModule = require('google-play-scraper');
const gplay = gplayModule.default || gplayModule;

interface AppData {
    appName: string;
    developer: string;
    rating: number | null;
    imgSrc: string;
    appId: string;
    appUrl: string;
    installs: number;
    category: string;
}



export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '200');
    const categoryParam = searchParams.get('category') || 'ALL';

    // Fetch maximum possible data from Google Play APIs
    // Google Play Scraper limits: search queries max 250, category browsing max 500
    const fetchLimit = query ? 250 : 500;

    // Get filter parameters
    const minInstalls = parseInt(searchParams.get('minInstalls') || '0');
    const maxInstalls = searchParams.get('maxInstalls') ? parseInt(searchParams.get('maxInstalls')!) : null;
    const minRating = parseFloat(searchParams.get('minRating') || '0');
    const maxRating = searchParams.get('maxRating') ? parseFloat(searchParams.get('maxRating')!) : null;

    console.log('🚀 MAXIMUM DATA FETCH - Starting scrape with params:', { query, page, limit, categoryParam, fetchLimit, minInstalls, maxInstalls, minRating, maxRating });

    try {
        let apps: AppData[] = [];

        // Handle search based on query and/or category
        if (query) {
            console.log(`🚀 FAST SEARCH: Requesting ${fetchLimit} results for query: "${query}"`);

            // Search by query with speed-optimized parameters
            const searchResults = await gplay.search({
                term: query,
                num: fetchLimit, // Request exactly 250 results (max for search)
                fullDetail: false, // SPEED OPTIMIZATION: Don't fetch full details
                price: 'all',
                lang: 'en',
                country: 'us'
                // Removed throttle for speed
            });

            console.log(`⚡ FAST SEARCH RESULTS: Received ${searchResults.length} apps in ${Date.now()}ms`);

            apps = searchResults.map((app: any) => {
                // Extract actual category from the app data
                const actualCategory = app.genreId || app.genre || app.category || 'UNKNOWN';

                // Parse installs from the basic data (faster than full detail)
                const installsText = app.installs || app.minInstalls || '0';
                const installs = typeof installsText === 'string'
                    ? parseInt(installsText.replace(/[^0-9]/g, '') || '0')
                    : parseInt(String(installsText) || '0');

                return {
                    appName: app.title,
                    developer: app.developer,
                    rating: app.score || null,
                    imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                    appId: app.appId,
                    appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                    installs: installs,
                    category: formatCategoryName(actualCategory)
                };
            });
        } else {
            // Search by category only
            console.log(`🚀 FAST CATEGORY: Requesting ${fetchLimit} results for category: "${categoryParam}"`);

            let categoryId;
            if (categoryParam === 'ALL') {
                // For ALL categories, fetch from multiple popular categories (SPEED OPTIMIZED)
                const categories = [
                    gplay.category.GAME,
                    gplay.category.SOCIAL,
                    gplay.category.ENTERTAINMENT,
                    gplay.category.PRODUCTIVITY
                ];

                console.log(`⚡ FAST ALL CATEGORIES: Fetching from ${categories.length} categories simultaneously`);

                const allCategoryApps = await Promise.all(
                    categories.map(async (cat) => {
                        try {
                            const categoryApps = await gplay.list({
                                category: cat,
                                collection: gplay.collection.TOP_FREE,
                                num: Math.floor(fetchLimit / categories.length),
                                fullDetail: false, // SPEED OPTIMIZATION: No full details
                                country: 'us',
                                language: 'en'
                                // Removed throttle for speed
                            });
                            console.log(`⚡ Fast Category ${cat}: ${categoryApps.length} apps`);
                            return categoryApps;
                        } catch (error) {
                            console.error(`❌ Error fetching category ${cat}:`, error);
                            return [];
                        }
                    })
                );

                apps = allCategoryApps.flat();
                console.log(`🎯 FAST ALL CATEGORIES TOTAL: ${apps.length} apps combined`);
            } else {
                // Map our category names to gplay category constants
                const categoryMap: { [key: string]: string } = {
                    'GAME': gplay.category.GAME,
                    'SOCIAL': gplay.category.SOCIAL,
                    'COMMUNICATION': gplay.category.COMMUNICATION,
                    'PRODUCTIVITY': gplay.category.PRODUCTIVITY,
                    'EDUCATION': gplay.category.EDUCATION,
                    'ENTERTAINMENT': gplay.category.ENTERTAINMENT,
                    'MUSIC_AND_AUDIO': gplay.category.MUSIC_AND_AUDIO,
                    'VIDEO_PLAYERS': gplay.category.VIDEO_PLAYERS,
                    'PHOTOGRAPHY': gplay.category.PHOTOGRAPHY,
                    'TOOLS': gplay.category.TOOLS,
                    'SHOPPING': gplay.category.SHOPPING,
                    'FINANCE': gplay.category.FINANCE,
                    'HEALTH_AND_FITNESS': gplay.category.HEALTH_AND_FITNESS,
                    'LIFESTYLE': gplay.category.LIFESTYLE,
                    'BOOKS_AND_REFERENCE': gplay.category.BOOKS_AND_REFERENCE,
                    // Additional categories
                    'SPORTS': gplay.category.SPORTS,
                    'MEDICAL': gplay.category.MEDICAL,
                    'MAPS_AND_NAVIGATION': gplay.category.MAPS_AND_NAVIGATION,
                    'AUTO_AND_VEHICLES': gplay.category.AUTO_AND_VEHICLES,
                    'PERSONALIZATION': gplay.category.PERSONALIZATION,
                    'DATING': gplay.category.DATING,
                    'FOOD_AND_DRINK': gplay.category.FOOD_AND_DRINK,
                    'HOUSE_AND_HOME': gplay.category.HOUSE_AND_HOME,
                    // Previously removed categories that are actually available
                    'NEWS_AND_MAGAZINES': gplay.category.NEWS_AND_MAGAZINES,
                    'WEATHER': gplay.category.WEATHER,
                    'TRAVEL_AND_LOCAL': gplay.category.TRAVEL_AND_LOCAL,
                    'BUSINESS': gplay.category.BUSINESS,
                    // Additional Google Play categories
                    'ART_AND_DESIGN': gplay.category.ART_AND_DESIGN,
                    'BEAUTY': gplay.category.BEAUTY,
                    'COMICS': gplay.category.COMICS,
                    'EVENTS': gplay.category.EVENTS,
                    'PARENTING': gplay.category.PARENTING
                };
                categoryId = categoryMap[categoryParam] || gplay.category.GAME;

                console.log(`⚡ FAST SINGLE CATEGORY: Fetching apps for category: ${categoryParam}`);

                let categoryApps: any[] = [];
                try {
                    // SPEED OPTIMIZED: Single fast request without complex multi-collection logic
                    categoryApps = await gplay.list({
                        category: categoryId,
                        collection: gplay.collection.TOP_FREE,
                        num: fetchLimit, // Request exactly 500 results (max for category)
                        fullDetail: false, // SPEED OPTIMIZATION: No full details
                        country: 'us',
                        language: 'en'
                        // Removed throttle for speed
                    });
                    console.log(`⚡ FAST CATEGORY RESULTS: ${categoryApps.length} apps for ${categoryParam}`);

                } catch (error) {
                    console.error(`❌ Error fetching category ${categoryParam}:`, error);
                    // Simple fallback to games if category fails
                    try {
                        categoryApps = await gplay.list({
                            category: gplay.category.GAME,
                            collection: gplay.collection.TOP_FREE,
                            num: fetchLimit,
                            fullDetail: false, // SPEED OPTIMIZATION
                            country: 'us',
                            language: 'en'
                        });
                        console.log(`🎮 Fast Fallback: ${categoryApps.length} games`);
                    } catch (fallbackError) {
                        console.error(`❌ Fallback failed:`, fallbackError);
                        categoryApps = [];
                    }
                }

                apps = categoryApps.map((app: any) => {
                    // Extract actual category from the app data
                    const actualCategory = app.genreId || app.genre || app.category || categoryParam;

                    // Parse installs from the basic data (faster than full detail)
                    const installsText = app.installs || app.minInstalls || '0';
                    const installs = typeof installsText === 'string'
                        ? parseInt(installsText.replace(/[^0-9]/g, '') || '0')
                        : parseInt(String(installsText) || '0');

                    return {
                        appName: app.title,
                        developer: app.developer,
                        rating: app.score || null,
                        imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                        appId: app.appId,
                        appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                        installs: installs,
                        category: formatCategoryName(actualCategory)
                    };
                });
            }
        }

        // Apply filters
        const filteredApps = apps.filter(app => {
            const installsValid = (!minInstalls || app.installs >= minInstalls) &&
                (maxInstalls === null || app.installs <= maxInstalls);
            const ratingValid = (!minRating || (app.rating !== null && app.rating >= minRating)) &&
                (maxRating === null || (app.rating !== null && app.rating <= maxRating));

            // Apply category filter if specified and not searching ALL
            // When fetching by category, the API already filters by category, so we should be more lenient
            const categoryValid = categoryParam === 'ALL' ||
                !query; // If no search query, we're browsing by category so accept all apps from that category

            return installsValid && ratingValid && categoryValid;
        });

        console.log(`Filtered ${apps.length} apps down to ${filteredApps.length} apps`);

        // Apply pagination
        const startIndex = (page - 1) * limit;
        const paginatedApps = filteredApps.slice(startIndex, startIndex + limit);
        const totalApps = filteredApps.length;
        const totalPages = Math.ceil(totalApps / limit);

        return NextResponse.json({
            apps: paginatedApps,
            total: totalApps,
            page,
            limit,
            totalPages,
            hasMore: page < totalPages,
            debug: {
                category: categoryParam,
                totalAppsBefore: apps.length,
                totalAppsAfter: paginatedApps.length,
                filters: { minInstalls, maxInstalls, minRating, maxRating }
            }
        });

    } catch (error: any) {
        console.error('Error scraping Google Play Store:', error);
        return NextResponse.json({
            error: 'Failed to fetch apps',
            details: error.message || 'Unknown error',
            category: categoryParam
        }, { status: 500 });
    }
}