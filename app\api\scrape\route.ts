import { NextResponse } from 'next/server';
import { formatCategoryName } from '../../constants/categories';
const gplayModule = require('google-play-scraper');
const gplay = gplayModule.default || gplayModule;

interface AppData {
    appName: string;
    developer: string;
    rating: number | null;
    imgSrc: string;
    appId: string;
    appUrl: string;
    installs: number;
    category: string;
}



export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '200');
    const categoryParam = searchParams.get('category') || 'ALL';

    // Fetch maximum possible data from Google Play APIs
    // Google Play Scraper limits: search queries max 250, category browsing max 500
    const fetchLimit = query ? 250 : 500;

    // Get filter parameters
    const minInstalls = parseInt(searchParams.get('minInstalls') || '0');
    const maxInstalls = searchParams.get('maxInstalls') ? parseInt(searchParams.get('maxInstalls')!) : null;
    const minRating = parseFloat(searchParams.get('minRating') || '0');
    const maxRating = searchParams.get('maxRating') ? parseFloat(searchParams.get('maxRating')!) : null;

    console.log('🚀 MAXIMUM DATA FETCH - Starting scrape with params:', { query, page, limit, categoryParam, fetchLimit, minInstalls, maxInstalls, minRating, maxRating });

    try {
        let apps: AppData[] = [];

        // Handle search based on query and/or category
        if (query) {
            console.log(`🔍 SEARCH MODE: Requesting maximum ${fetchLimit} results for query: "${query}"`);

            // Search by query with optimized parameters for maximum results
            const searchResults = await gplay.search({
                term: query,
                num: fetchLimit, // Request exactly 250 results (max for search)
                fullDetail: true,
                price: 'all',
                lang: 'en',
                country: 'us',
                throttle: 10 // Add throttling to avoid rate limits
            });

            console.log(`✅ SEARCH RESULTS: Received ${searchResults.length} apps from Google Play Scraper`);

            apps = searchResults.map((app: any) => {
                // Extract actual category from the app data
                const actualCategory = app.genreId || app.genre || app.category || 'UNKNOWN';

                return {
                    appName: app.title,
                    developer: app.developer,
                    rating: app.score || null,
                    imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                    appId: app.appId,
                    appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                    installs: parseInt(app.installs?.replace(/[^0-9]/g, '') || '0'),
                    category: formatCategoryName(actualCategory)
                };
            });
        } else {
            // Search by category only
            console.log(`📱 CATEGORY MODE: Requesting maximum ${fetchLimit} results for category: "${categoryParam}"`);

            let categoryId;
            if (categoryParam === 'ALL') {
                // For ALL categories, fetch from multiple popular categories
                const categories = [
                    gplay.category.GAME,
                    gplay.category.SOCIAL,
                    gplay.category.ENTERTAINMENT,
                    gplay.category.PRODUCTIVITY,
                    gplay.category.TOOLS,
                    gplay.category.COMMUNICATION
                ];

                console.log(`🔄 ALL CATEGORIES MODE: Fetching from ${categories.length} categories`);

                const allCategoryApps = await Promise.all(
                    categories.map(async (cat) => {
                        try {
                            const categoryApps = await gplay.list({
                                category: cat,
                                collection: gplay.collection.TOP_FREE,
                                num: Math.floor(fetchLimit / categories.length),
                                fullDetail: true,
                                country: 'us',
                                language: 'en',
                                throttle: 10 // Add throttling to avoid rate limits
                            });
                            console.log(`✅ Category ${cat}: Received ${categoryApps.length} apps`);
                            return categoryApps;
                        } catch (error) {
                            console.error(`❌ Error fetching category ${cat}:`, error);
                            return [];
                        }
                    })
                );

                apps = allCategoryApps.flat();
                console.log(`🎯 ALL CATEGORIES TOTAL: Combined ${apps.length} apps from all categories`);
            } else {
                // Map our category names to gplay category constants
                const categoryMap: { [key: string]: string } = {
                    'GAME': gplay.category.GAME,
                    'SOCIAL': gplay.category.SOCIAL,
                    'COMMUNICATION': gplay.category.COMMUNICATION,
                    'PRODUCTIVITY': gplay.category.PRODUCTIVITY,
                    'EDUCATION': gplay.category.EDUCATION,
                    'ENTERTAINMENT': gplay.category.ENTERTAINMENT,
                    'MUSIC_AND_AUDIO': gplay.category.MUSIC_AND_AUDIO,
                    'VIDEO_PLAYERS': gplay.category.VIDEO_PLAYERS,
                    'PHOTOGRAPHY': gplay.category.PHOTOGRAPHY,
                    'TOOLS': gplay.category.TOOLS,
                    'SHOPPING': gplay.category.SHOPPING,
                    'FINANCE': gplay.category.FINANCE,
                    'HEALTH_AND_FITNESS': gplay.category.HEALTH_AND_FITNESS,
                    'LIFESTYLE': gplay.category.LIFESTYLE,
                    'BOOKS_AND_REFERENCE': gplay.category.BOOKS_AND_REFERENCE,
                    // Additional categories
                    'SPORTS': gplay.category.SPORTS,
                    'MEDICAL': gplay.category.MEDICAL,
                    'MAPS_AND_NAVIGATION': gplay.category.MAPS_AND_NAVIGATION,
                    'AUTO_AND_VEHICLES': gplay.category.AUTO_AND_VEHICLES,
                    'PERSONALIZATION': gplay.category.PERSONALIZATION,
                    'DATING': gplay.category.DATING,
                    'FOOD_AND_DRINK': gplay.category.FOOD_AND_DRINK,
                    'HOUSE_AND_HOME': gplay.category.HOUSE_AND_HOME,
                    // Previously removed categories that are actually available
                    'NEWS_AND_MAGAZINES': gplay.category.NEWS_AND_MAGAZINES,
                    'WEATHER': gplay.category.WEATHER,
                    'TRAVEL_AND_LOCAL': gplay.category.TRAVEL_AND_LOCAL,
                    'BUSINESS': gplay.category.BUSINESS,
                    // Additional Google Play categories
                    'ART_AND_DESIGN': gplay.category.ART_AND_DESIGN,
                    'BEAUTY': gplay.category.BEAUTY,
                    'COMICS': gplay.category.COMICS,
                    'EVENTS': gplay.category.EVENTS,
                    'PARENTING': gplay.category.PARENTING
                };
                categoryId = categoryMap[categoryParam] || gplay.category.GAME;

                console.log(`🎯 SINGLE CATEGORY: Fetching apps for category: ${categoryParam} (mapped to: ${categoryId})`);

                let categoryApps;
                try {
                    // Request maximum possible results with optimized parameters
                    categoryApps = await gplay.list({
                        category: categoryId,
                        collection: gplay.collection.TOP_FREE,
                        num: fetchLimit, // Request exactly 500 results (max for category)
                        fullDetail: true,
                        country: 'us',
                        language: 'en',
                        throttle: 10 // Add throttling to avoid rate limits
                    });
                    console.log(`✅ PRIMARY FETCH: Received ${categoryApps.length} apps for category ${categoryParam}`);

                    // If we got fewer than expected, try additional collections to maximize results
                    if (categoryApps.length < 400) {
                        console.log(`⚠️ Got only ${categoryApps.length} apps, trying additional collections...`);

                        const additionalCollections = [
                            gplay.collection.NEW_FREE,
                            gplay.collection.TOP_GROSSING
                        ];

                        for (const collection of additionalCollections) {
                            try {
                                console.log(`🔄 Trying additional collection: ${collection}`);
                                const additionalApps = await gplay.list({
                                    category: categoryId,
                                    collection: collection,
                                    num: fetchLimit,
                                    fullDetail: true,
                                    country: 'us',
                                    language: 'en',
                                    throttle: 10
                                });

                                console.log(`✅ Additional collection ${collection}: Received ${additionalApps.length} apps`);

                                // Add unique apps only
                                const uniqueApps = additionalApps.filter((newApp: any) =>
                                    !categoryApps.some((existingApp: any) => existingApp.appId === newApp.appId)
                                );

                                categoryApps = [...categoryApps, ...uniqueApps];
                                console.log(`📊 Total unique apps after ${collection}: ${categoryApps.length}`);

                                // Stop if we have enough apps
                                if (categoryApps.length >= 450) {
                                    console.log(`🎯 Reached sufficient apps (${categoryApps.length}), stopping additional fetches`);
                                    break;
                                }

                            } catch (error) {
                                console.error(`❌ Error fetching additional collection ${collection}:`, error);
                            }
                        }
                    }

                } catch (error) {
                    console.error(`❌ Error fetching category ${categoryParam}:`, error);
                    // Fallback to games if category fails
                    try {
                        categoryApps = await gplay.list({
                            category: gplay.category.GAME,
                            collection: gplay.collection.TOP_FREE,
                            num: fetchLimit,
                            fullDetail: true,
                            country: 'us',
                            language: 'en',
                            throttle: 10
                        });
                        console.log(`🎮 Fallback: fetched ${categoryApps.length} games instead`);
                    } catch (fallbackError) {
                        console.error(`❌ Fallback also failed:`, fallbackError);
                        categoryApps = [];
                    }
                }

                console.log(`🏁 FINAL CATEGORY RESULTS: ${categoryApps.length} apps for category ${categoryParam}`);

                apps = categoryApps.map((app: any) => {
                    // Extract actual category from the app data
                    const actualCategory = app.genreId || app.genre || app.category || categoryParam;

                    return {
                        appName: app.title,
                        developer: app.developer,
                        rating: app.score || null,
                        imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                        appId: app.appId,
                        appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                        installs: parseInt(app.installs?.replace(/[^0-9]/g, '') || '0'),
                        category: formatCategoryName(actualCategory)
                    };
                });
            }
        }

        // Apply filters
        const filteredApps = apps.filter(app => {
            const installsValid = (!minInstalls || app.installs >= minInstalls) &&
                (maxInstalls === null || app.installs <= maxInstalls);
            const ratingValid = (!minRating || (app.rating !== null && app.rating >= minRating)) &&
                (maxRating === null || (app.rating !== null && app.rating <= maxRating));

            // Apply category filter if specified and not searching ALL
            // When fetching by category, the API already filters by category, so we should be more lenient
            const categoryValid = categoryParam === 'ALL' ||
                !query; // If no search query, we're browsing by category so accept all apps from that category

            return installsValid && ratingValid && categoryValid;
        });

        console.log(`Filtered ${apps.length} apps down to ${filteredApps.length} apps`);

        // Apply pagination
        const startIndex = (page - 1) * limit;
        const paginatedApps = filteredApps.slice(startIndex, startIndex + limit);
        const totalApps = filteredApps.length;
        const totalPages = Math.ceil(totalApps / limit);

        return NextResponse.json({
            apps: paginatedApps,
            total: totalApps,
            page,
            limit,
            totalPages,
            hasMore: page < totalPages,
            debug: {
                category: categoryParam,
                totalAppsBefore: apps.length,
                totalAppsAfter: paginatedApps.length,
                filters: { minInstalls, maxInstalls, minRating, maxRating }
            }
        });

    } catch (error: any) {
        console.error('Error scraping Google Play Store:', error);
        return NextResponse.json({
            error: 'Failed to fetch apps',
            details: error.message || 'Unknown error',
            category: categoryParam
        }, { status: 500 });
    }
}