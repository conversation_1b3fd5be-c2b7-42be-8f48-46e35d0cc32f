'use client';

import { use, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import AppDetailCard from '../../components/AppDetailCard';
import { Loader2, ArrowLeft, Home } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-hot-toast';

interface AppDetails {
    appId: string;
    appName: string;
    developer: string;
    description: string;
    rating: number | null;
    ratingCount: number;
    screenshots: string[];
    icon: string;
    category: string;
    additionalInfo: Record<string, string>;
    appUrl: string;
    supportEmail?: string;
    developerWebsite?: string;
    developerAddress?: string;
    installs?: number;
}

export default function AppDetailsPage({ params }: { params: Promise<{ appId: string }> }) {
    const { appId } = use(params);
    const [appDetails, setAppDetails] = useState<AppDetails | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const router = useRouter();

    useEffect(() => {
        const fetchAppDetails = async () => {
            setLoading(true);
            try {
                const response = await fetch(`/api/app-details?appId=${appId}`);

                if (!response.ok) {
                    throw new Error('Failed to fetch app details');
                }

                const data = await response.json();
                setAppDetails(data);
                // Update document title with app name
                document.title = `${data.appName} - App Scraper`;
            } catch (error) {
                console.error('Error fetching app details:', error);
                setError('Failed to fetch app details. Please try again later.');
                toast.error('Failed to load app details');
            } finally {
                setLoading(false);
            }
        };

        if (appId) {
            fetchAppDetails();
        }
    }, [appId]);

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Navigation Bar */}
            <div className="sticky top-[64px] z-40 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-14">
                        <div className="flex items-center">
                            <Link
                                href="/"
                                className="flex items-center text-gray-700 dark:text-gray-200 hover:text-primary dark:hover:text-primary transition-colors"
                            >
                                <ArrowLeft size={16} className="mr-2" />
                                <span className="font-medium">Back to search</span>
                            </Link>
                        </div>

                        <Link
                            href="/"
                            className="flex items-center text-gray-700 dark:text-gray-200 hover:text-primary dark:hover:text-primary transition-colors"
                        >
                            <Home size={18} className="mr-2" />
                            <span className="font-medium">Home</span>
                        </Link>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="container mx-auto px-4 py-6">
                {loading ? (
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-20 flex flex-col items-center justify-center">
                        <Loader2 size={50} className="animate-spin text-primary mb-4" />
                        <p className="text-gray-600 dark:text-gray-300 text-lg">Loading app details...</p>
                    </div>
                ) : error ? (
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-20 text-center">
                        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 text-red-500 mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h2 className="text-2xl font-bold mb-2 text-gray-800 dark:text-white">Error Loading Data</h2>
                        <p className="text-gray-600 dark:text-gray-300 mb-6">{error}</p>
                        <button
                            onClick={() => router.push('/')}
                            className="px-6 py-3 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
                        >
                            Go back to search
                        </button>
                    </div>
                ) : appDetails ? (
                    <AppDetailCard appDetails={appDetails} />
                ) : (
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-20 text-center">
                        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-yellow-100 text-yellow-500 mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h2 className="text-2xl font-bold mb-2 text-gray-800 dark:text-white">App Not Found</h2>
                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                            We couldn't find this app. It might not exist or has been removed from the Google Play Store.
                        </p>
                        <button
                            onClick={() => router.push('/')}
                            className="px-6 py-3 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
                        >
                            Go back to search
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
} 