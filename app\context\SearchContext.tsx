'use client';

import { createContext, useContext, useState, ReactNode } from 'react';
import { toast } from 'react-hot-toast';

interface AppData {
    appId: string;
    appName: string;
    developer: string;
    rating: number | null;
    imgSrc: string;
    appUrl: string;
    category: string;
}

interface SearchContextType {
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    searchResults: AppData[];
    setSearchResults: (results: AppData[]) => void;
    hasSearched: boolean;
    setHasSearched: (value: boolean) => void;
    currentPage: number;
    setCurrentPage: (page: number) => void;
    totalApps: number;
    setTotalApps: (total: number) => void;
    selectedCategory: string;
    setSelectedCategory: (category: string) => void;
    handleCategorySearch: (category: string) => Promise<void>;
    isLoading: boolean;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

export function SearchProvider({ children }: { children: ReactNode }) {
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState<AppData[]>([]);
    const [hasSearched, setHasSearched] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalApps, setTotalApps] = useState(0);
    const [selectedCategory, setSelectedCategory] = useState('ALL');
    const [isLoading, setIsLoading] = useState(false);

    const handleCategorySearch = async (category: string) => {
        setIsLoading(true);
        setHasSearched(true);
        setCurrentPage(1);

        try {
            const queryParam = searchQuery.trim() ? `&query=${encodeURIComponent(searchQuery.trim())}` : '';
            const response = await fetch(`/api/scrape?category=${category}&page=1&limit=200${queryParam}`);

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Failed to fetch apps');
            }

            const data = await response.json();
            setSearchResults(data.apps || []);
            setTotalApps(data.total || 0);

            if (data.apps?.length === 0) {
                if (category === 'ALL') {
                    toast.error('No apps found. Please try a different search.');
                } else {
                    toast.error(`No apps found in ${category.toLowerCase().replace(/_/g, ' ')} category`);
                }
            }
        } catch (error) {
            console.error('Error fetching category apps:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to fetch apps');
            setSearchResults([]);
            setTotalApps(0);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <SearchContext.Provider
            value={{
                searchQuery,
                setSearchQuery,
                searchResults,
                setSearchResults,
                hasSearched,
                setHasSearched,
                currentPage,
                setCurrentPage,
                totalApps,
                setTotalApps,
                selectedCategory,
                setSelectedCategory,
                handleCategorySearch,
                isLoading,
            }}
        >
            {children}
        </SearchContext.Provider>
    );
}

export function useSearch() {
    const context = useContext(SearchContext);
    if (context === undefined) {
        throw new Error('useSearch must be used within a SearchProvider');
    }
    return context;
} 