import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Navbar from "./components/Navbar";
import { Toaster } from "react-hot-toast";
import ThemeProvider from "./components/ThemeProvider";
import { SearchProvider } from './context/SearchContext';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "App Scraper - Google Play Store App Search",
  description: "Search and discover apps from the Google Play Store",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-50 dark:bg-gray-900 min-h-screen`}
      >
        <ThemeProvider>
          <SearchProvider>
            <div className="min-h-screen flex flex-col">
              <div className="sticky top-0 z-50 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <Navbar />
              </div>
              <main className="flex-1">
                {children}
              </main>
            </div>
            <Toaster position="bottom-right" />
          </SearchProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
