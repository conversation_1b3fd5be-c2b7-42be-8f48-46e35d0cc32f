import { NextResponse } from 'next/server';
import { PlayStoreService } from '@/app/services/playstore.service';

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url);
        const query = searchParams.get('query');
        const category = searchParams.get('category') || undefined;
        const language = searchParams.get('language') || 'en';
        const country = searchParams.get('country') || 'US';
        const maxResults = parseInt(searchParams.get('maxResults') || '100');
        const action = searchParams.get('action') || 'search';

        console.log('Received request with params:', { query, category, language, country, maxResults, action });

        const playStoreService = new PlayStoreService();

        let response;
        switch (action) {
            case 'search':
                if (!query) {
                    return NextResponse.json(
                        { error: 'Query parameter is required for search' },
                        { status: 400 }
                    );
                }
                console.log('Performing search for:', query);
                response = await playStoreService.searchApps(query, {
                    language,
                    country,
                    maxResults,
                    category
                });
                break;

            case 'top':
                console.log('Fetching top apps for category:', category);
                response = await playStoreService.getTopApps(category, {
                    language,
                    country,
                    maxResults
                });
                break;

            case 'similar':
                const packageName = searchParams.get('packageName');
                if (!packageName) {
                    return NextResponse.json(
                        { error: 'Package name is required for similar apps' },
                        { status: 400 }
                    );
                }
                console.log('Fetching similar apps for:', packageName);
                response = await playStoreService.getSimilarApps(packageName, maxResults);
                break;

            default:
                return NextResponse.json(
                    { error: 'Invalid action specified' },
                    { status: 400 }
                );
        }

        console.log(`Found ${response?.length || 0} results`);

        return NextResponse.json({
            success: true,
            data: response || [],
            total: response?.length || 0,
            metadata: {
                query,
                category,
                language,
                country,
                maxResults,
                action
            }
        });

    } catch (error) {
        console.error('API Error:', error);
        return NextResponse.json(
            {
                error: 'Failed to fetch data from Play Store',
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    }
} 