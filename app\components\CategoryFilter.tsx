'use client';

import { PLAY_STORE_CATEGORIES } from '../constants/categories';
import { useSearch } from '../context/SearchContext';
import { Loader2, X } from 'lucide-react';

export default function CategoryFilter() {
    const { selectedCategory, setSelectedCategory, handleCategorySearch, isLoading } = useSearch();

    const onCategoryClick = (categoryId: string) => {
        if (!isLoading) {
            setSelectedCategory(categoryId);
            handleCategorySearch(categoryId);
        }
    };

    const onClearCategory = (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent triggering the category click
        if (!isLoading) {
            setSelectedCategory('');
            // Clear results when unselecting category
            handleCategorySearch('');
        }
    };

    return (
        <div className="flex flex-wrap justify-center gap-2">
            {PLAY_STORE_CATEGORIES.map((category) => (
                <button
                    key={category.id}
                    onClick={() => onCategoryClick(category.id)}
                    disabled={isLoading}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors relative ${selectedCategory === category.id
                        ? 'bg-yellow-300 text-gray-900 pr-8'
                        : 'bg-white/10 text-white hover:bg-white/20'
                        } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''} ${isLoading && selectedCategory === category.id ? 'flex items-center gap-2' : ''
                        }`}
                >
                    {isLoading && selectedCategory === category.id ? (
                        <>
                            <Loader2 size={14} className="animate-spin" />
                            {category.name}
                        </>
                    ) : (
                        <>
                            {category.name}
                            {selectedCategory === category.id && !isLoading && (
                                <X
                                    size={14}
                                    className="absolute right-2 top-1/2 transform -translate-y-1/2 hover:bg-gray-200 rounded-full p-0.5 cursor-pointer"
                                    onClick={onClearCategory}
                                />
                            )}
                        </>
                    )}
                </button>
            ))}
        </div>
    );
}