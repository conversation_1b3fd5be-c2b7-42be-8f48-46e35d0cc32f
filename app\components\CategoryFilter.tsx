'use client';

import { PLAY_STORE_CATEGORIES } from '../constants/categories';
import { useSearch } from '../context/SearchContext';
import { Loader2 } from 'lucide-react';

export default function CategoryFilter() {
    const { selectedCategory, setSelectedCategory, handleCategorySearch, isLoading } = useSearch();

    const onCategoryClick = (categoryId: string) => {
        if (!isLoading) {
            setSelectedCategory(categoryId);
            handleCategorySearch(categoryId);
        }
    };

    const onClearCategory = () => {
        if (!isLoading) {
            setSelectedCategory('');
            // Clear results when unselecting category
            handleCategorySearch('');
        }
    };

    return (
        <div className="flex flex-wrap justify-center gap-2">
            {/* Clear Category Button */}
            <button
                onClick={onClearCategory}
                disabled={isLoading}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${selectedCategory === ''
                    ? 'bg-yellow-300 text-gray-900'
                    : 'bg-white/10 text-white hover:bg-white/20'
                    } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
                All Categories
            </button>

            {PLAY_STORE_CATEGORIES.map((category) => (
                <button
                    key={category.id}
                    onClick={() => onCategoryClick(category.id)}
                    disabled={isLoading}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${selectedCategory === category.id
                        ? 'bg-yellow-300 text-gray-900'
                        : 'bg-white/10 text-white hover:bg-white/20'
                        } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''} ${isLoading && selectedCategory === category.id ? 'flex items-center gap-2' : ''
                        }`}
                >
                    {isLoading && selectedCategory === category.id ? (
                        <>
                            <Loader2 size={14} className="animate-spin" />
                            {category.name}
                        </>
                    ) : (
                        category.name
                    )}
                </button>
            ))}
        </div>
    );
}