'use client';

import { useState } from 'react';
import SearchBar from './components/SearchBar';
import AppCard from './components/AppCard';
import CategoryFilter from './components/CategoryFilter';
import AppFilters from './components/AppFilters';
import { Smartphone, Loader2, Search, ArrowDown, Store, ChevronLeft, ChevronRight, Download, Star } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useSearch } from './context/SearchContext';

export default function Home() {
  const [isLoading, setIsLoading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [filters, setFilters] = useState({
    minInstalls: '',
    maxInstalls: '',
    minRating: '',
    maxRating: '',
  });
  const [allApps, setAllApps] = useState<any[]>([]); // Store all unfiltered results
  const {
    searchQuery,
    setSearchQuery,
    searchResults: apps,
    setSearchResults: setApps,
    hasSearched,
    setHasSearched,
    currentPage,
    setCurrentPage,
    totalApps,
    setTotalApps,
    selectedCategory,
  } = useSearch();

  const itemsPerPage = 200;

  // Function to apply filters to results
  const applyFilters = (appsToFilter: any[]) => {
    return appsToFilter.filter(app => {
      let passesFilters = true;

      // Filter by category (if not ALL)
      if (selectedCategory !== 'ALL') {
        const appCategory = app.category?.toUpperCase() || '';
        const searchCategory = selectedCategory.toUpperCase();
        passesFilters = passesFilters && appCategory === searchCategory;
      }

      // Filter by installations
      if (filters.minInstalls && app.installs !== undefined) {
        let appInstalls = 0;

        if (typeof app.installs === 'number') {
          appInstalls = app.installs;
        } else if (typeof app.installs === 'string') {
          // Handle string formats like "1M+", "1B+", etc.
          const cleanInstalls = app.installs.toLowerCase().trim();
          if (cleanInstalls.match(/10b\+/i)) {
            appInstalls = 10000000000; // 10 billion
          } else if (cleanInstalls.includes('b')) {
            const num = parseFloat(cleanInstalls.replace(/[^0-9.]/g, ''));
            appInstalls = !isNaN(num) ? num * 1000000000 : 0;
          } else if (cleanInstalls.includes('m')) {
            const num = parseFloat(cleanInstalls.replace(/[^0-9.]/g, ''));
            appInstalls = !isNaN(num) ? num * 1000000 : 0;
          } else if (cleanInstalls.includes('k')) {
            const num = parseFloat(cleanInstalls.replace(/[^0-9.]/g, ''));
            appInstalls = !isNaN(num) ? num * 1000 : 0;
          } else {
            appInstalls = parseInt(cleanInstalls.replace(/[^0-9]/g, '')) || 0;
          }
        }

        const minInstallCount = parseInt(filters.minInstalls);
        if (!isNaN(minInstallCount)) {
          passesFilters = passesFilters && appInstalls >= minInstallCount;
          console.log(`App ${app.appName}: installs=${appInstalls}, minRequired=${minInstallCount}, passes=${passesFilters}`);
        }
      }

      // Filter by rating
      if (filters.minRating && app.rating !== null && app.rating !== undefined) {
        const minRating = parseFloat(filters.minRating);
        const appRating = typeof app.rating === 'number' ? app.rating : parseFloat(app.rating.toString());

        if (!isNaN(minRating) && !isNaN(appRating)) {
          passesFilters = passesFilters && appRating >= minRating;
          console.log(`App ${app.appName}: rating=${appRating}, minRequired=${minRating}, passes=${passesFilters}`);
        }
      }

      if (filters.maxRating && app.rating !== null && app.rating !== undefined) {
        const maxRating = parseFloat(filters.maxRating);
        const appRating = typeof app.rating === 'number' ? app.rating : parseFloat(app.rating.toString());

        if (!isNaN(maxRating) && !isNaN(appRating)) {
          passesFilters = passesFilters && appRating <= maxRating;
          console.log(`App ${app.appName}: rating=${appRating}, maxAllowed=${maxRating}, passes=${passesFilters}`);
        }
      }

      return passesFilters;
    });
  };

  const handleSearch = async (query: string) => {
    if (!query.trim() && selectedCategory === 'ALL') {
      toast.error('Please enter a search query or select a category');
      return;
    }

    setIsLoading(true);
    setHasSearched(true);
    setSearchQuery(query);
    setCurrentPage(1);

    try {
      const queryParams = new URLSearchParams({
        ...(query.trim() && { query: query.trim() }),
        page: '1',
        limit: itemsPerPage.toString(),
        category: selectedCategory,
        fetchLimit: '500',
        ...(filters.minInstalls && { minInstalls: filters.minInstalls }),
        ...(filters.maxInstalls && { maxInstalls: filters.maxInstalls }),
        ...(filters.minRating && { minRating: filters.minRating }),
        ...(filters.maxRating && { maxRating: filters.maxRating })
      });

      console.log('Search query params:', queryParams.toString());

      const response = await fetch(`/api/scrape?${queryParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch apps');
      }

      const data = await response.json();
      const fetchedApps = data.apps || [];

      // Apply client-side filters to ensure consistency
      const filteredApps = applyFilters(fetchedApps);

      setAllApps(fetchedApps);
      setApps(filteredApps);
      setTotalApps(data.total || 0);

      if (filteredApps.length === 0) {
        toast.error('No apps found matching your criteria');
      }
    } catch (error) {
      console.error('Error searching apps:', error);
      toast.error('Failed to search for apps. Please try again.');
      setApps([]);
      setTotalApps(0);
      setAllApps([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = async (newFilters: typeof filters) => {
    console.log('New filters:', newFilters);

    // Convert string values to proper numeric values for API
    const processedFilters = {
      ...newFilters,
      minInstalls: newFilters.minInstalls ? String(convertToNumber(newFilters.minInstalls)) : '',
      minRating: newFilters.minRating ? newFilters.minRating : '',
      maxRating: newFilters.maxRating ? newFilters.maxRating : ''
    };

    console.log('Processed filters:', processedFilters);
    setFilters(processedFilters);
    setCurrentPage(1);

    // Trigger a new search with the updated filters
    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams({
        ...(searchQuery.trim() && { query: searchQuery.trim() }),
        page: '1',
        limit: itemsPerPage.toString(),
        category: selectedCategory,
        fetchLimit: '500',
        ...(processedFilters.minInstalls && { minInstalls: processedFilters.minInstalls }),
        ...(processedFilters.maxInstalls && { maxInstalls: processedFilters.maxInstalls }),
        ...(processedFilters.minRating && { minRating: processedFilters.minRating }),
        ...(processedFilters.maxRating && { maxRating: processedFilters.maxRating })
      });

      console.log('API request params:', queryParams.toString());

      const response = await fetch(`/api/scrape?${queryParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch apps');
      }

      const data = await response.json();
      console.log('API response:', data);

      const fetchedApps = data.apps || [];

      // Apply client-side filters to ensure consistency
      const filteredApps = applyFilters(fetchedApps);

      setAllApps(fetchedApps);
      setApps(filteredApps);
      setTotalApps(data.total || 0);

      if (filteredApps.length === 0) {
        toast.error('No apps match the selected filters');
      }
    } catch (error) {
      console.error('Error applying filters:', error);
      toast.error('Failed to apply filters. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to convert installation counts to numbers
  const convertToNumber = (value: string): number => {
    if (!value) return 0;

    // If it's already a number string, parse it
    if (/^\d+$/.test(value)) {
      return parseInt(value);
    }

    // Handle K, M, B suffixes
    const cleanValue = value.toLowerCase().replace(/[+]/g, '').trim();
    if (cleanValue.endsWith('b')) {
      return parseFloat(cleanValue) * 1000000000;
    } else if (cleanValue.endsWith('m')) {
      return parseFloat(cleanValue) * 1000000;
    } else if (cleanValue.endsWith('k')) {
      return parseFloat(cleanValue) * 1000;
    }

    return parseInt(value) || 0;
  };

  // Update the filter buttons click handler
  const handleFilterButtonClick = (type: 'installs' | 'rating', value: string) => {
    console.log(`Filter button clicked: type=${type}, value=${value}`);

    if (type === 'installs') {
      const numericValue = convertToNumber(value);
      console.log(`Converting ${value} to ${numericValue}`);
      handleFilterChange({ ...filters, minInstalls: String(numericValue) });
    } else if (type === 'rating') {
      // For rating, we want to keep the original string value (e.g., "4.5")
      const ratingValue = value.replace('+', '');
      handleFilterChange({
        ...filters,
        minRating: ratingValue,
        maxRating: '5.0' // Set maximum rating to 5.0 when filtering by minimum rating
      });
    }
  };

  const handleFilterRemove = (filterKey: keyof typeof filters) => {
    const newFilters = { ...filters, [filterKey]: '' };
    setFilters(newFilters);

    // Reapply remaining filters to all apps
    const filteredApps = applyFilters(allApps);
    setApps(filteredApps);
    setTotalApps(filteredApps.length);
    setCurrentPage(1);
  };

  const clearAllFilters = async () => {
    // Clear filter state
    const clearedFilters = {
      minInstalls: '',
      maxInstalls: '',
      minRating: '',
      maxRating: '',
    };
    setFilters(clearedFilters);
    setCurrentPage(1);

    // Trigger a new search without filters
    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams({
        ...(searchQuery.trim() && { query: searchQuery.trim() }),
        page: '1',
        limit: itemsPerPage.toString(),
        category: selectedCategory,
        fetchLimit: '500',
      });

      const response = await fetch(`/api/scrape?${queryParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch apps');
      }

      const data = await response.json();
      const fetchedApps = data.apps || [];
      setAllApps(fetchedApps);
      setApps(fetchedApps);
      setTotalApps(data.total || 0);
    } catch (error) {
      console.error('Error clearing filters:', error);
      toast.error('Failed to clear filters. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePageChange = async (page: number) => {
    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams({
        ...(searchQuery.trim() && { query: searchQuery.trim() }),
        page: page.toString(),
        limit: itemsPerPage.toString(),
        category: selectedCategory,
        fetchLimit: '500',
        ...(filters.minInstalls && { minInstalls: filters.minInstalls }),
        ...(filters.maxInstalls && { maxInstalls: filters.maxInstalls }),
        ...(filters.minRating && { minRating: filters.minRating }),
        ...(filters.maxRating && { maxRating: filters.maxRating }),
      });

      const response = await fetch(`/api/scrape?${queryParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch apps');
      }

      const data = await response.json();
      const fetchedApps = data.apps || [];
      setAllApps(fetchedApps);
      setApps(fetchedApps);
      setTotalApps(data.total || 0);
      setCurrentPage(page);

      window.scrollTo({ top: 0, behavior: 'smooth' });
    } catch (error) {
      console.error('Error fetching page:', error);
      toast.error('Failed to load page. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const totalPages = Math.ceil(totalApps / itemsPerPage);

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always show first page
      pageNumbers.push(1);

      if (currentPage <= 3) {
        // Near the start
        for (let i = 2; i <= 4; i++) {
          pageNumbers.push(i);
        }
        pageNumbers.push('...');
        pageNumbers.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Near the end
        pageNumbers.push('...');
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        // Middle
        pageNumbers.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pageNumbers.push(i);
        }
        pageNumbers.push('...');
        pageNumbers.push(totalPages);
      }
    }

    return pageNumbers;
  };

  const formatInstallCount = (count: number) => {
    if (count >= 1000000000) {
      return `${(count / 1000000000).toFixed(1)}B`;
    }
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-20 px-4">
        <div className="max-w-5xl mx-auto text-center text-white">
          <div className="inline-flex items-center justify-center p-4 bg-white/10 rounded-full backdrop-blur-sm mb-6">
            <Store size={40} className="text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">
            App<span className="text-yellow-300">Scraper</span>
          </h1>
          <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            Search for any app on the Google Play Store and get detailed information, screenshots, and more
          </p>

          <div className="max-w-2xl mx-auto focus-within:outline-none">
            <SearchBar onSearch={handleSearch} isLoading={isLoading} initialValue={searchQuery} />
            <div className="mt-6">
              <CategoryFilter />
            </div>
          </div>

          {!hasSearched && (
            <div className="mt-12 animate-bounce">
              <ArrowDown size={24} className="mx-auto text-white/70" />
            </div>
          )}
        </div>
      </div>

      {/* Results Section */}
      <div className="flex-1 bg-gray-50 dark:bg-gray-900 py-8 px-4">
        <div className="max-w-7xl mx-auto">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-20">
              <Loader2 size={50} className="animate-spin text-primary mb-4" />
              <p className="text-gray-600 dark:text-gray-300">Searching for apps...</p>
            </div>
          ) : (
            hasSearched && (
              <div>
                {apps.length > 0 ? (
                  <>
                    <div className="flex flex-col gap-6 mb-8">
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center justify-between">
                          <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                            Search Results
                          </h2>
                          <button
                            onClick={async () => {
                              setIsDownloading(true);
                              try {
                                const queryParams = new URLSearchParams({
                                  ...(searchQuery.trim() && { query: searchQuery.trim() }),
                                  page: currentPage.toString(),
                                  limit: itemsPerPage.toString(),
                                  category: selectedCategory,
                                  ...(filters.minInstalls && { minInstalls: filters.minInstalls }),
                                  ...(filters.maxInstalls && { maxInstalls: filters.maxInstalls }),
                                  ...(filters.minRating && { minRating: filters.minRating }),
                                  ...(filters.maxRating && { maxRating: filters.maxRating }),
                                });

                                const response = await fetch(
                                  `/api/download?${queryParams}`
                                );

                                if (!response.ok) {
                                  const errorData = await response.json();
                                  throw new Error(errorData.error || 'Download failed');
                                }

                                // Get the filename from the Content-Disposition header if available
                                const contentDisposition = response.headers.get('Content-Disposition');
                                const filenameMatch = contentDisposition?.match(/filename="(.+)"/);
                                const filename = filenameMatch
                                  ? filenameMatch[1]
                                  : searchQuery.trim()
                                    ? `app_search_${searchQuery.trim()}_page${currentPage}.csv`
                                    : `${selectedCategory.toLowerCase()}_apps_page${currentPage}.csv`;

                                // Create blob from response
                                const blob = await response.blob();
                                const url = window.URL.createObjectURL(blob);

                                // Create temporary link and trigger download
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = filename;
                                document.body.appendChild(a);
                                a.click();

                                // Cleanup
                                window.URL.revokeObjectURL(url);
                                document.body.removeChild(a);

                                toast.success('Download completed');
                              } catch (error) {
                                console.error('Download error:', error);
                                toast.error(error instanceof Error ? error.message : 'Failed to download CSV');
                              } finally {
                                setIsDownloading(false);
                              }
                            }}
                            className="px-6 py-2.5 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2 border border-gray-200 dark:border-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled={isLoading || isDownloading}
                          >
                            {isDownloading ? (
                              <>
                                <Loader2 className="w-4 h-4 animate-spin" />
                                Downloading...
                              </>
                            ) : (
                              <>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                  <polyline points="7 10 12 15 17 10" />
                                  <line x1="12" y1="15" x2="12" y2="3" />
                                </svg>
                                Export to CSV
                              </>
                            )}
                          </button>
                        </div>
                        <div className="flex flex-wrap items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center">
                            <Search size={14} className="mr-2" />
                            <span className="font-medium text-gray-900 dark:text-white">{searchQuery}</span>
                          </div>
                          <span className="mx-2">•</span>
                          <span>{totalApps} apps found</span>
                          <span className="mx-2">•</span>
                          <span>Page {currentPage} of {Math.ceil(totalApps / itemsPerPage)}</span>

                          {/* Active Filters Display */}
                          {(filters.minInstalls || filters.minRating) && (
                            <>
                              <span className="mx-2">•</span>
                              <div className="flex flex-wrap items-center gap-2">
                                <span className="text-blue-400">Filtered by:</span>
                                {filters.minInstalls && (
                                  <div className="flex items-center gap-1 bg-[#252b3b] text-gray-300 px-2 py-1 rounded text-xs">
                                    <span>
                                      {parseInt(filters.minInstalls) >= 1000000000
                                        ? '1B+'
                                        : formatInstallCount(parseInt(filters.minInstalls)) + '+'} installs
                                    </span>
                                    <button
                                      onClick={() => handleFilterRemove('minInstalls')}
                                      className="ml-1 hover:text-blue-400 transition-colors"
                                    >
                                      ×
                                    </button>
                                  </div>
                                )}
                                {filters.minRating && (
                                  <div className="flex items-center gap-1 bg-[#252b3b] text-gray-300 px-2 py-1 rounded text-xs">
                                    <span>{filters.minRating}+ rating</span>
                                    <button
                                      onClick={() => handleFilterRemove('minRating')}
                                      className="ml-1 hover:text-blue-400 transition-colors"
                                    >
                                      ×
                                    </button>
                                  </div>
                                )}
                                <button
                                  onClick={clearAllFilters}
                                  className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                                >
                                  Clear all
                                </button>
                              </div>
                            </>
                          )}
                        </div>
                      </div>

                      {/* Filters */}
                      <div className="bg-[#1a1f2e] rounded-lg p-4">
                        <AppFilters onFilterChange={handleFilterChange} />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                      {apps.map((app) => (
                        <AppCard key={app.appId} app={app} />
                      ))}
                    </div>

                    {/* Pagination Controls */}
                    {totalPages > 1 && (
                      <div className="mt-8 flex justify-center items-center space-x-2">
                        <button
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1 || isLoading}
                          className="p-2 rounded-lg bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ChevronLeft size={20} />
                        </button>

                        <div className="flex items-center space-x-1">
                          {getPageNumbers().map((pageNum, index) => (
                            pageNum === '...' ? (
                              <span key={`ellipsis-${index}`} className="px-4 py-2 text-gray-400">...</span>
                            ) : (
                              <button
                                key={pageNum}
                                onClick={() => typeof pageNum === 'number' && handlePageChange(pageNum)}
                                disabled={isLoading}
                                className={`px-4 py-2 rounded-lg ${currentPage === pageNum
                                  ? 'bg-primary text-white'
                                  : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                                  }`}
                              >
                                {pageNum}
                              </button>
                            )
                          ))}
                        </div>

                        <button
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages || isLoading}
                          className="p-2 rounded-lg bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ChevronRight size={20} />
                        </button>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="bg-[#1a1f2e] rounded-xl p-8">
                    <div className="flex flex-col items-center justify-center mb-8">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-700 mb-4">
                        <Search size={24} className="text-gray-400" />
                      </div>
                      <h3 className="text-xl font-semibold mb-2 text-white">No apps found</h3>
                      <p className="text-gray-400">
                        We couldn't find any apps matching "". Try adjusting your filters or search term.
                      </p>
                    </div>

                    {/* Active Filters Display */}
                    <div className="flex items-center justify-center gap-2 mb-6 text-sm">
                      <span className="text-blue-400">Active filters:</span>
                      {filters.minInstalls && (
                        <div className="flex items-center gap-1 bg-[#252b3b] text-gray-300 px-2 py-1 rounded text-xs">
                          <span>
                            {parseInt(filters.minInstalls) >= 1000000000
                              ? '1B+'
                              : formatInstallCount(parseInt(filters.minInstalls)) + '+'} installs
                          </span>
                          <button
                            onClick={() => handleFilterRemove('minInstalls')}
                            className="ml-1 hover:text-blue-400 transition-colors"
                          >
                            ×
                          </button>
                        </div>
                      )}
                      <button
                        onClick={clearAllFilters}
                        className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                      >
                        Clear all filters
                      </button>
                    </div>

                    {/* Filter Options */}
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center gap-2 text-gray-300 min-w-[140px]">
                          <Download size={14} />
                          <span className="text-sm">Minimum Installations</span>
                        </div>
                        <div className="flex gap-1.5">
                          {[
                            { label: '1K+', value: '1000' },
                            { label: '10K+', value: '10000' },
                            { label: '100K+', value: '100000' },
                            { label: '1M+', value: '1000000' },
                            { label: '10M+', value: '10000000' },
                            { label: '100M+', value: '100000000' },
                            { label: '1B+', value: '1000000000' }
                          ].map(({ label, value }) => (
                            <button
                              key={label}
                              onClick={() => handleFilterButtonClick('installs', value)}
                              className={`px-2 py-1 text-xs rounded transition-colors ${filters.minInstalls === value
                                ? 'bg-blue-500 text-white'
                                : 'bg-[#252b3b] text-gray-300 hover:bg-[#2f364a]'
                                }`}
                            >
                              {label}
                            </button>
                          ))}
                          <input
                            type="number"
                            placeholder="Custom"
                            className="w-24 px-2 py-1 text-xs rounded bg-[#252b3b] text-gray-300 border border-gray-700 focus:border-blue-500 focus:outline-none"
                            onChange={(e) => handleFilterChange({ ...filters, minInstalls: e.target.value })}
                          />
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <div className="flex items-center gap-2 text-gray-300 min-w-[140px]">
                          <Star size={14} />
                          <span className="text-sm">Minimum Rating</span>
                        </div>
                        <div className="flex gap-1.5">
                          {[
                            { label: '4.5+', value: '4.5' },
                            { label: '4.0+', value: '4.0' },
                            { label: '3.5+', value: '3.5' },
                            { label: '3.0+', value: '3.0' }
                          ].map(({ label, value }) => (
                            <button
                              key={label}
                              onClick={() => handleFilterButtonClick('rating', value)}
                              className={`px-2 py-1 text-xs rounded transition-colors ${filters.minRating === value
                                ? 'bg-blue-500 text-white'
                                : 'bg-[#252b3b] text-gray-300 hover:bg-[#2f364a]'
                                }`}
                            >
                              {label}
                            </button>
                          ))}
                          <input
                            type="number"
                            placeholder="Custom"
                            min="0"
                            max="5"
                            step="0.1"
                            className="w-24 px-2 py-1 text-xs rounded bg-[#252b3b] text-gray-300 border border-gray-700 focus:border-blue-500 focus:outline-none"
                            onChange={(e) => handleFilterChange({ ...filters, minRating: e.target.value })}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="mt-8">
                      <SearchBar onSearch={handleSearch} isLoading={isLoading} initialValue={searchQuery} />
                    </div>
                  </div>
                )}
              </div>
            )
          )}

          {/* Features Section - Only visible when no search has been performed */}
          {!hasSearched && !isLoading && (
            <div className="max-w-5xl mx-auto mt-8">
              <h2 className="text-2xl font-bold text-center text-gray-800 dark:text-white mb-12">
                Features
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 mb-4">
                    <Search size={20} />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-800 dark:text-white">App Search</h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Search for any app available on the Google Play Store
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mb-4">
                    <Smartphone size={20} />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Detailed Info</h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    View detailed information, screenshots, and descriptions for each app
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 mb-4">
                    <Store size={20} />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-800 dark:text-white">Direct Links</h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Get direct links to the official app on the Google Play Store
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-6 px-4">
        <div className="max-w-7xl mx-auto text-center text-sm text-gray-500 dark:text-gray-400">
          <p>App data sourced from Google Play Store. This is for educational purposes only.</p>
        </div>
      </footer>
    </div>
  );
}
