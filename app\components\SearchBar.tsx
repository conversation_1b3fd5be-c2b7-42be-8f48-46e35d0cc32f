'use client';

import { useState, FormEvent, useEffect, useRef } from 'react';
import { Search, Loader2, X, Bookmark } from 'lucide-react';

interface SearchBarProps {
    onSearch: (query: string) => void;
    isLoading: boolean;
    initialValue?: string;
}

const SAMPLE_SEARCHES = [
    'Facebook',
    'Messenger',
    'Instagram',
    'WhatsApp',
    'Twitter',
    'TikTok',
    'Spotify',
    'Netflix'
];

export default function SearchBar({ onSearch, isLoading, initialValue = '' }: SearchBarProps) {
    const [query, setQuery] = useState(initialValue);
    const [placeholderIndex, setPlaceholderIndex] = useState(0);
    const [isFocused, setIsFocused] = useState(false);
    const wrapperRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        setQuery(initialValue);
    }, [initialValue]);

    // Rotate through sample searches for the placeholder
    useEffect(() => {
        const interval = setInterval(() => {
            setPlaceholderIndex((prev) => (prev + 1) % SAMPLE_SEARCHES.length);
        }, 3000);

        return () => clearInterval(interval);
    }, []);

    // Handle clicks outside to close the popular suggestions
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
                setIsFocused(false);
            }
        }

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (query.trim()) {
            onSearch(query.trim());
            setIsFocused(false);
        }
    };

    const clearSearch = () => {
        setQuery('');
    };

    const handlePopularSearch = (term: string) => {
        setQuery(term);
        onSearch(term);
        setIsFocused(false);
    };

    return (
        <div ref={wrapperRef} className="relative w-full mx-auto outline-none focus:outline-none">
            <form onSubmit={handleSubmit} className="w-full mx-auto outline-none focus:outline-none">
                <div
                    className={`relative flex items-center w-full overflow-hidden rounded-full transition-all duration-200 ${isFocused ? 'ring-2 ring-opacity-50 ring-primary/50 shadow-lg' : 'shadow-md'
                        }`}
                >
                    <div className="absolute left-4 text-gray-400 dark:text-gray-500 z-10">
                        <Search size={20} />
                    </div>

                    <input
                        type="text"
                        value={query}
                        onChange={(e) => setQuery(e.target.value)}
                        placeholder={`Search for ${SAMPLE_SEARCHES[placeholderIndex]}...`}
                        className="w-full py-4 pl-12 pr-24 bg-white dark:bg-gray-800 border-0 rounded-full text-gray-800 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-0 focus:border-0 shadow-none"
                        disabled={isLoading}
                        onFocus={() => setIsFocused(true)}
                    />

                    {query && !isLoading && (
                        <button
                            type="button"
                            onClick={clearSearch}
                            className="absolute right-20 p-2 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
                            aria-label="Clear search"
                        >
                            <X size={18} />
                        </button>
                    )}

                    <button
                        type="submit"
                        className={`absolute right-3 px-4 py-2 rounded-full transition-all duration-200 no-outline focus:outline-none ${isLoading
                            ? 'bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'
                            : 'bg-primary text-white hover:bg-primary/90'
                            }`}
                        disabled={isLoading}
                    >
                        {isLoading ? (
                            <Loader2 size={18} className="animate-spin" />
                        ) : (
                            <span className="flex items-center">
                                Search
                            </span>
                        )}
                    </button>
                </div>
            </form>

            {/* Popular suggestions dropdown */}
            {isFocused && !isLoading && (
                <div className="absolute mt-2 w-full bg-white dark:bg-gray-800 rounded-xl shadow-lg z-50 overflow-hidden border border-gray-100 dark:border-gray-700 animate-fadeIn">
                    <div className="p-2 flex items-center border-b border-gray-100 dark:border-gray-700">
                        <Bookmark size={14} className="text-primary mr-2" />
                        <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Popular Searches</span>
                    </div>
                    <div className="max-h-[200px] overflow-y-auto">
                        {SAMPLE_SEARCHES.map((term) => (
                            <button
                                key={term}
                                type="button"
                                onClick={() => handlePopularSearch(term)}
                                className="w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors flex items-center"
                            >
                                <Search size={14} className="mr-2 text-gray-400" />
                                {term}
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
} 