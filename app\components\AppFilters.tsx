import { useState } from 'react';
import { Star, Download, Filter } from 'lucide-react';

type FilterKeys = 'minInstalls' | 'maxInstalls' | 'minRating' | 'maxRating';

interface Filters {
    minInstalls: string;
    maxInstalls: string;
    minRating: string;
    maxRating: string;
}

interface AppFiltersProps {
    onFilterChange: (filters: Filters) => void;
    activeFilters?: Partial<Filters>;
}

const AppFilters = ({ onFilterChange, activeFilters }: AppFiltersProps) => {
    const [filters, setFilters] = useState<Filters>({
        minInstalls: '',
        maxInstalls: '',
        minRating: '',
        maxRating: '',
        ...activeFilters
    });

    const handleFilterChange = (key: FilterKeys, value: string) => {
        // If clicking the same value, clear it
        const newValue = filters[key] === value ? '' : value;
        const newFilters = { ...filters, [key]: newValue };
        setFilters(newFilters);
        onFilterChange(newFilters);
    };

    const handleInputChange = (key: FilterKeys, value: string) => {
        const newFilters = { ...filters, [key]: value };
        setFilters(newFilters);
        onFilterChange(newFilters);
    };

    const installationRanges = [
        { label: '1K+', value: '1000' },
        { label: '10K+', value: '10000' },
        { label: '100K+', value: '100000' },
        { label: '1M+', value: '1000000' },
        { label: '10M+', value: '10000000' },
        { label: '100M+', value: '100000000' },
        { label: '1B+', value: '1000000000' }
    ];

    const ratingRanges = [
        { label: '4.5+', value: '4.5' },
        { label: '4.0+', value: '4.0' },
        { label: '3.5+', value: '3.5' },
        { label: '3.0+', value: '3.0' },
    ];

    return (
        <div className="flex flex-col space-y-4">
            {/* Minimum Installations Section */}
            <div className="flex items-center space-x-2">
                <div className="flex items-center gap-2 text-gray-300 min-w-[140px]">
                    <Download size={14} />
                    <span className="text-sm">Minimum Installations</span>
                </div>
                <div className="flex gap-1.5">
                    {installationRanges.map((range) => (
                        <button
                            key={range.value}
                            onClick={() => handleFilterChange('minInstalls', range.value)}
                            className={`px-2 py-1 text-xs rounded transition-colors ${filters.minInstalls === range.value
                                ? 'bg-blue-500 text-white'
                                : 'bg-[#252b3b] text-gray-300 hover:bg-[#2f364a]'
                                }`}
                        >
                            {range.label}
                        </button>
                    ))}
                    <input
                        type="number"
                        placeholder="Custom"
                        value={!installationRanges.some(r => r.value === filters.minInstalls) ? filters.minInstalls : ''}
                        onChange={(e) => handleInputChange('minInstalls', e.target.value)}
                        className="w-24 px-2 py-1 text-xs rounded bg-[#252b3b] text-gray-300 border border-gray-700 focus:border-blue-500 focus:outline-none"
                    />
                </div>
            </div>

            {/* Minimum Rating Section */}
            <div className="flex items-center space-x-2">
                <div className="flex items-center gap-2 text-gray-300 min-w-[140px]">
                    <Star size={14} />
                    <span className="text-sm">Minimum Rating</span>
                </div>
                <div className="flex gap-1.5">
                    {ratingRanges.map((range) => (
                        <button
                            key={range.value}
                            onClick={() => handleFilterChange('minRating', range.value)}
                            className={`px-2 py-1 text-xs rounded transition-colors ${filters.minRating === range.value
                                ? 'bg-blue-500 text-white'
                                : 'bg-[#252b3b] text-gray-300 hover:bg-[#2f364a]'
                                }`}
                        >
                            {range.label}
                        </button>
                    ))}
                    <input
                        type="number"
                        placeholder="Custom"
                        min="0"
                        max="5"
                        step="0.1"
                        value={!ratingRanges.some(r => r.value === filters.minRating) ? filters.minRating : ''}
                        onChange={(e) => handleInputChange('minRating', e.target.value)}
                        className="w-24 px-2 py-1 text-xs rounded bg-[#252b3b] text-gray-300 border border-gray-700 focus:border-blue-500 focus:outline-none"
                    />
                </div>
            </div>

            {/* Clear Filters */}
            <div className="flex justify-end">
                <button
                    onClick={() => {
                        const clearedFilters = {
                            minInstalls: '',
                            maxInstalls: '',
                            minRating: '',
                            maxRating: '',
                        };
                        setFilters(clearedFilters);
                        onFilterChange(clearedFilters);
                    }}
                    className="text-xs text-gray-400 hover:text-blue-400 transition-colors"
                >
                    Clear all filters
                </button>
            </div>
        </div>
    );
};

export default AppFilters; 