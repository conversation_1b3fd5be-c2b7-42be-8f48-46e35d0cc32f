import { useState } from 'react';
import { Star, Download, Filter, Search } from 'lucide-react';

type FilterKeys = 'minInstalls' | 'maxInstalls' | 'minRating' | 'maxRating';

interface Filters {
    minInstalls: string;
    maxInstalls: string;
    minRating: string;
    maxRating: string;
}

interface AppFiltersProps {
    onFilterChange: (filters: Filters) => void;
    activeFilters?: Partial<Filters>;
}

const AppFilters = ({ onFilterChange, activeFilters }: AppFiltersProps) => {
    const [filters, setFilters] = useState<Filters>({
        minInstalls: '',
        maxInstalls: '',
        minRating: '',
        maxRating: '',
        ...activeFilters
    });

    // Temporary state for custom inputs (before applying)
    const [tempCustomInstalls, setTempCustomInstalls] = useState('');
    const [tempCustomRating, setTempCustomRating] = useState('');

    const handleFilterChange = (key: FilterKeys, value: string) => {
        // If clicking the same value, clear it
        const newValue = filters[key] === value ? '' : value;
        const newFilters = { ...filters, [key]: newValue };
        setFilters(newFilters);
        onFilterChange(newFilters);

        // Clear temp values when using preset buttons
        if (key === 'minInstalls') setTempCustomInstalls('');
        if (key === 'minRating') setTempCustomRating('');
    };

    // Apply custom installs filter
    const applyCustomInstalls = () => {
        if (tempCustomInstalls.trim()) {
            const newFilters = { ...filters, minInstalls: tempCustomInstalls.trim() };
            setFilters(newFilters);
            onFilterChange(newFilters);
        }
    };

    // Apply custom rating filter
    const applyCustomRating = () => {
        if (tempCustomRating.trim()) {
            const newFilters = { ...filters, minRating: tempCustomRating.trim() };
            setFilters(newFilters);
            onFilterChange(newFilters);
        }
    };

    const installationRanges = [
        { label: '1K+', value: '1000' },
        { label: '10K+', value: '10000' },
        { label: '100K+', value: '100000' },
        { label: '1M+', value: '1000000' },
        { label: '10M+', value: '10000000' },
        { label: '100M+', value: '100000000' },
        { label: '1B+', value: '1000000000' }
    ];

    const ratingRanges = [
        { label: '4.5+', value: '4.5' },
        { label: '4.0+', value: '4.0' },
        { label: '3.5+', value: '3.5' },
        { label: '3.0+', value: '3.0' },
    ];

    return (
        <div className="flex flex-col space-y-4">
            {/* Minimum Installations Section */}
            <div className="flex items-center space-x-2">
                <div className="flex items-center gap-2 text-gray-300 min-w-[140px]">
                    <Download size={14} />
                    <span className="text-sm">Minimum Installations</span>
                </div>
                <div className="flex gap-1.5">
                    {installationRanges.map((range) => (
                        <button
                            key={range.value}
                            onClick={() => handleFilterChange('minInstalls', range.value)}
                            className={`px-2 py-1 text-xs rounded transition-colors ${filters.minInstalls === range.value
                                ? 'bg-blue-500 text-white'
                                : 'bg-[#252b3b] text-gray-300 hover:bg-[#2f364a]'
                                }`}
                        >
                            {range.label}
                        </button>
                    ))}
                    <div className="flex items-center gap-1">
                        <input
                            type="number"
                            placeholder="Custom"
                            value={tempCustomInstalls}
                            onChange={(e) => setTempCustomInstalls(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && applyCustomInstalls()}
                            className="w-20 px-2 py-1 text-xs rounded bg-[#252b3b] text-gray-300 border border-gray-700 focus:border-blue-500 focus:outline-none"
                        />
                        <button
                            onClick={applyCustomInstalls}
                            disabled={!tempCustomInstalls.trim()}
                            className="px-2 py-1 text-xs rounded bg-blue-500 text-white hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
                            title="Apply custom filter"
                        >
                            <Search size={12} />
                        </button>
                    </div>
                </div>
            </div>

            {/* Minimum Rating Section */}
            <div className="flex items-center space-x-2">
                <div className="flex items-center gap-2 text-gray-300 min-w-[140px]">
                    <Star size={14} />
                    <span className="text-sm">Minimum Rating</span>
                </div>
                <div className="flex gap-1.5">
                    {ratingRanges.map((range) => (
                        <button
                            key={range.value}
                            onClick={() => handleFilterChange('minRating', range.value)}
                            className={`px-2 py-1 text-xs rounded transition-colors ${filters.minRating === range.value
                                ? 'bg-blue-500 text-white'
                                : 'bg-[#252b3b] text-gray-300 hover:bg-[#2f364a]'
                                }`}
                        >
                            {range.label}
                        </button>
                    ))}
                    <div className="flex items-center gap-1">
                        <input
                            type="number"
                            placeholder="Custom"
                            min="0"
                            max="5"
                            step="0.1"
                            value={tempCustomRating}
                            onChange={(e) => setTempCustomRating(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && applyCustomRating()}
                            className="w-20 px-2 py-1 text-xs rounded bg-[#252b3b] text-gray-300 border border-gray-700 focus:border-blue-500 focus:outline-none"
                        />
                        <button
                            onClick={applyCustomRating}
                            disabled={!tempCustomRating.trim()}
                            className="px-2 py-1 text-xs rounded bg-blue-500 text-white hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
                            title="Apply custom filter"
                        >
                            <Search size={12} />
                        </button>
                    </div>
                </div>
            </div>

            {/* Clear Filters */}
            <div className="flex justify-end">
                <button
                    onClick={() => {
                        const clearedFilters = {
                            minInstalls: '',
                            maxInstalls: '',
                            minRating: '',
                            maxRating: '',
                        };
                        setFilters(clearedFilters);
                        setTempCustomInstalls('');
                        setTempCustomRating('');
                        onFilterChange(clearedFilters);
                    }}
                    className="text-xs text-gray-400 hover:text-blue-400 transition-colors"
                >
                    Clear all filters
                </button>
            </div>
        </div>
    );
};

export default AppFilters;