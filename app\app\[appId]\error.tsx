'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AlertTriangle } from 'lucide-react';

export default function Error({
    error,
    reset,
}: {
    error: Error & { digest?: string };
    reset: () => void;
}) {
    const router = useRouter();

    useEffect(() => {
        console.error('App details error:', error);
    }, [error]);

    return (
        <div className="flex justify-center items-center py-20">
            <div className="max-w-md w-full text-center">
                <div className="flex justify-center mb-4">
                    <AlertTriangle size={50} className="text-red-500" />
                </div>
                <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                    Something went wrong
                </h2>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                    There was an error loading the app details. Please try again later.
                </p>
                <div className="flex flex-col sm:flex-row justify-center gap-4">
                    <button
                        onClick={reset}
                        className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
                    >
                        Try again
                    </button>
                    <button
                        onClick={() => router.push('/')}
                        className="px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                    >
                        Go back to home
                    </button>
                </div>
            </div>
        </div>
    );
} 