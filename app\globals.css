@import "tailwindcss";

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  /* Horizontal scroll with momentum scrolling for touch devices */
  .scroll-snap-x {
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
  }
  
  .scroll-snap-center {
    scroll-snap-align: center;
  }
  
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
  }
  
  /* Cursor styles for draggable elements */
  .cursor-grab {
    cursor: grab;
  }
  
  .cursor-grabbing {
    cursor: grabbing !important;
  }
  
  /* Active scrolling styles */
  .scrolling-active {
    cursor: grabbing !important;
    user-select: none;
  }
  
  /* Overscroll behavior for touch devices */
  .overscroll-x-contain {
    overscroll-behavior-x: contain;
  }
}

/* Enable smooth scrolling for all elements */
html {
  scroll-behavior: smooth;
}

/* Fix for Chrome scroll jittering on Mac */
@media screen and (min-width: 0\0) {
  body {
    overflow-x: hidden;
  }
}

/* Force hardware acceleration for smoother scrolling */
.hw-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  --play-store-bg: #1a1b26;
  --play-store-card-bg: #1E293B;
  --play-store-header-bg: #232539;
  --play-store-text: #FFFFFF;
  --play-store-text-secondary: rgba(255, 255, 255, 0.7);
  --play-store-border: #2D3748;
  --play-store-primary: #8AB4F8;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a1b26;
    --foreground: #ededed;
  }
}

body {
  background: var(--play-store-bg);
  color: var(--play-store-text);
  font-family: "Roboto", Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.bg-primary {
  @apply bg-blue-600;
}

.text-primary {
  @apply text-blue-600 dark:text-blue-400;
}

.hover\:bg-primary\/90:hover {
  @apply hover:bg-blue-600/90;
}

/* Remove outline for search inputs */
input[type="text"],
input[type="search"],
.no-outline:focus,
.no-outline:active {
  outline: none !important;
  box-shadow: none !important;
}

/* Dark mode styles */
.dark {
  color-scheme: dark;
}

/* Custom ScrollBar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}
