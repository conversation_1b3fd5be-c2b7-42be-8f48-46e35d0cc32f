export const PLAY_STORE_CATEGORIES = [
    { id: 'ALL', name: 'All Categories' },
    { id: 'GAME', name: 'Games' },
    { id: 'SOCIAL', name: 'Social' },
    { id: 'COMMUNICATION', name: 'Communication' },
    { id: 'PRODUCTIVITY', name: 'Productivity' },
    { id: 'EDUCATION', name: 'Education' },
    { id: 'ENTERTAINMENT', name: 'Entertainment' },
    { id: 'MUSIC_AND_AUDIO', name: 'Music & Audio' },
    { id: 'VIDEO_PLAYERS', name: 'Video Players' },
    { id: 'PHOTOGRAPHY', name: 'Photography' },
    { id: 'TOOLS', name: 'Tools' },
    { id: 'SHOPPING', name: 'Shopping' },
    { id: 'FINANCE', name: 'Finance' },
    { id: 'HEALTH_AND_FITNESS', name: 'Health & Fitness' },
    { id: 'LIFESTYLE', name: 'Lifestyle' },
    { id: 'BOOKS_AND_REFERENCE', name: 'Books & Reference' },
    // Additional categories available in Google Play Store:
    { id: 'SPORTS', name: 'Sports' },
    { id: 'MEDICAL', name: 'Medical' },
    { id: 'MAPS_AND_NAVIGATION', name: 'Maps & Navigation' },
    { id: 'AUTO_AND_VEHICLES', name: 'Auto & Vehicles' },
    { id: 'PERSONALIZATION', name: 'Personalization' },
    { id: 'DATING', name: 'Dating' },
    { id: 'FOOD_AND_DRINK', name: 'Food & Drink' },
    { id: 'HOUSE_AND_HOME', name: 'House & Home' },
    // Previously removed categories that are actually available:
    { id: 'NEWS_AND_MAGAZINES', name: 'News & Magazines' },
    { id: 'WEATHER', name: 'Weather' },
    { id: 'TRAVEL_AND_LOCAL', name: 'Travel & Local' },
    { id: 'BUSINESS', name: 'Business' },
    // Additional categories from Google Play Store:
    { id: 'ART_AND_DESIGN', name: 'Art & Design' },
    { id: 'BEAUTY', name: 'Beauty' },
    { id: 'COMICS', name: 'Comics' },
    { id: 'EVENTS', name: 'Events' },
    { id: 'PARENTING', name: 'Parenting' }
];

// Map Google Play's internal category IDs to human-readable names
export const GOOGLE_PLAY_CATEGORY_NAMES: { [key: string]: string } = {
    // Main categories
    'GAME': 'Games',
    'SOCIAL': 'Social',
    'COMMUNICATION': 'Communication',
    'PRODUCTIVITY': 'Productivity',
    'EDUCATION': 'Education',
    'ENTERTAINMENT': 'Entertainment',
    'MUSIC_AND_AUDIO': 'Music & Audio',
    'VIDEO_PLAYERS': 'Video Players & Editors',
    'PHOTOGRAPHY': 'Photography',
    'TOOLS': 'Tools',
    'SHOPPING': 'Shopping',
    'FINANCE': 'Finance',
    'HEALTH_AND_FITNESS': 'Health & Fitness',
    'LIFESTYLE': 'Lifestyle',
    'BOOKS_AND_REFERENCE': 'Books & Reference',
    'SPORTS': 'Sports',
    'MEDICAL': 'Medical',
    'MAPS_AND_NAVIGATION': 'Maps & Navigation',
    'AUTO_AND_VEHICLES': 'Auto & Vehicles',
    'PERSONALIZATION': 'Personalization',
    'DATING': 'Dating',
    'FOOD_AND_DRINK': 'Food & Drink',
    'HOUSE_AND_HOME': 'House & Home',

    // Additional Google Play categories that might appear
    'ART_AND_DESIGN': 'Art & Design',
    'BEAUTY': 'Beauty',
    'BUSINESS': 'Business',
    'COMICS': 'Comics',
    'EVENTS': 'Events',
    'LIBRARIES_AND_DEMO': 'Libraries & Demo',
    'NEWS_AND_MAGAZINES': 'News & Magazines',
    'PARENTING': 'Parenting',
    'TRAVEL_AND_LOCAL': 'Travel & Local',
    'WEATHER': 'Weather',
    'WORD': 'Word',
    'ARCADE': 'Arcade',
    'ACTION': 'Action',
    'ADVENTURE': 'Adventure',
    'BOARD': 'Board',
    'CARD': 'Card',
    'CASINO': 'Casino',
    'CASUAL': 'Casual',
    'EDUCATIONAL': 'Educational',
    'MUSIC': 'Music',
    'PUZZLE': 'Puzzle',
    'RACING': 'Racing',
    'ROLE_PLAYING': 'Role Playing',
    'SIMULATION': 'Simulation',
    'SPORTS_GAMES': 'Sports Games',
    'STRATEGY': 'Strategy',
    'TRIVIA': 'Trivia',
    'WORD_GAMES': 'Word Games'
};

/**
 * Convert Google Play's internal category ID to a human-readable name
 */
export function formatCategoryName(categoryId: string): string {
    if (!categoryId) return 'Unknown';

    // Convert to uppercase and clean up
    const cleanId = categoryId.toUpperCase().trim();

    // Return mapped name or fallback to formatted version
    return GOOGLE_PLAY_CATEGORY_NAMES[cleanId] ||
        cleanId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}