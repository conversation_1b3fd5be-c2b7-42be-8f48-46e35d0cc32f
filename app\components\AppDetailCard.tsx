'use client';

import Image from 'next/image';
import { Star, Download, Calendar, Tag, User, Info, ExternalLink, Smartphone, ImageIcon, Award, Calendar as CalendarIcon, Clock, FileDown, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useState, useRef, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface AppDetailCardProps {
    appDetails: {
        appId: string;
        appName: string;
        developer: string;
        description: string;
        rating: number | null;
        ratingCount: number;
        screenshots: string[];
        icon: string;
        category: string;
        additionalInfo: Record<string, string>;
        appUrl: string;
        supportEmail?: string;
        developerWebsite?: string;
        developerAddress?: string;
        installs?: number;
    };
}

// Helper function to format install count
const formatInstalls = (installs: number): string => {
    if (installs >= 1000000000) {
        return `${(installs / 1000000000).toFixed(1)}B+`;
    } else if (installs >= 1000000) {
        return `${(installs / 1000000).toFixed(1)}M+`;
    } else if (installs >= 1000) {
        return `${(installs / 1000).toFixed(1)}K+`;
    }
    return `${installs}+`;
};

export default function AppDetailCard({ appDetails }: AppDetailCardProps) {
    const [iconError, setIconError] = useState(false);
    const [imageErrors, setImageErrors] = useState<Record<number, boolean>>({});
    const [showFullDescription, setShowFullDescription] = useState(false);
    const [fullViewImage, setFullViewImage] = useState<string | null>(null);
    const [activeScreenshotIndex, setActiveScreenshotIndex] = useState(0);
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    const [isDownloading, setIsDownloading] = useState(false);

    const {
        appName,
        developer,
        description,
        rating,
        ratingCount,
        screenshots = [],
        icon,
        category,
        additionalInfo,
        appUrl,
        supportEmail,
        developerWebsite,
        developerAddress,
        installs
    } = appDetails;

    // Filter out any empty screenshot URLs before displaying
    const validScreenshots = screenshots.filter(url => url && url.trim() !== '');

    // Function to proxy image URLs through our API
    const getProxiedImageUrl = (url: string) => {
        if (!url) return '';
        // If already a relative URL (our proxy), return as is
        if (url.startsWith('/api/proxy')) return url;
        // Otherwise, proxy it
        return `/api/proxy?url=${encodeURIComponent(url)}`;
    };

    const handleImageError = (index: number) => {
        setImageErrors(prev => ({
            ...prev,
            [index]: true
        }));
    };

    // Open full view of an image
    const openFullView = (img: string, index: number) => {
        setFullViewImage(img);
        setActiveScreenshotIndex(index);
    };

    // Close full view
    const closeFullView = () => {
        setFullViewImage(null);
    };

    // Create a truncated description for display purposes
    const shortDescription = description && description.length > 300
        ? `${description.substring(0, 300)}...`
        : description;

    const renderRatingStars = (rating: number) => {
        return (
            <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                        key={star}
                        size={16}
                        className={`${star <= Math.round(rating) ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'} mr-1`}
                        fill={star <= Math.round(rating) ? 'currentColor' : 'none'}
                    />
                ))}
            </div>
        );
    };

    // Helper to check if we have any screenshots
    const hasScreenshots = validScreenshots.length > 0;
    // Helper to check if we have any additional info
    const hasAdditionalInfo = additionalInfo && Object.keys(additionalInfo).length > 0;
    // Helper to check if we have any support info
    const hasSupport = !!supportEmail || !!developerWebsite || !!developerAddress;
    // Helper to check if category is valid
    const validCategory = category && category.toLowerCase() !== 'app';

    // Handle scrolling logic for showing 3 screenshots at a time
    const scrollToScreenshot = (index: number) => {
        if (!scrollContainerRef.current) return;

        const container = scrollContainerRef.current;
        const itemWidth = 180 + 12; // width of item + gap

        // Calculate which page this index is on (3 screenshots per page)
        const page = Math.floor(index / 3);
        const pageStartIndex = page * 3;

        // Scroll to the start of the page containing the target screenshot
        container.scrollTo({
            left: pageStartIndex * itemWidth,
            behavior: 'smooth'
        });

        setActiveScreenshotIndex(index);
    };

    const handleScrollLeft = () => {
        // Calculate current page
        const currentPage = Math.floor(activeScreenshotIndex / 3);

        // If we're on first page, stay there, otherwise go to previous page
        const newPage = Math.max(0, currentPage - 1);
        const newIndex = newPage * 3; // First screenshot on the new page

        scrollToScreenshot(newIndex);
    };

    const handleScrollRight = () => {
        // Calculate current page and total pages
        const currentPage = Math.floor(activeScreenshotIndex / 3);
        const totalPages = Math.ceil(validScreenshots.length / 3);

        // If we're on last page, stay there, otherwise go to next page
        const newPage = Math.min(totalPages - 1, currentPage + 1);
        const newIndex = newPage * 3; // First screenshot on the new page

        // Make sure we don't go past the end of the array
        scrollToScreenshot(Math.min(newIndex, validScreenshots.length - 1));
    };

    // Update active screenshot index when scrolling
    const handleScroll = () => {
        if (!scrollContainerRef.current) return;

        const container = scrollContainerRef.current;
        const scrollLeft = container.scrollLeft;
        const itemWidth = 180 + 12; // width of item + gap

        // Calculate which item is most visible based on scroll position
        const index = Math.round(scrollLeft / itemWidth);

        // Make sure the index is valid
        if (index !== activeScreenshotIndex && index >= 0 && index < validScreenshots.length) {
            setActiveScreenshotIndex(index);
        }
    };

    // Add scroll event listener
    useEffect(() => {
        const container = scrollContainerRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);

            return () => {
                container.removeEventListener('scroll', handleScroll);
            };
        }
    }, [activeScreenshotIndex, validScreenshots.length]);

    // Enable draggable scrolling
    useEffect(() => {
        const container = scrollContainerRef.current;
        if (!container) return;

        let isDown = false;
        let startX: number;
        let scrollLeft: number;

        const handleMouseDown = (e: MouseEvent) => {
            isDown = true;
            container.classList.add('scrolling-active');
            startX = e.pageX - container.offsetLeft;
            scrollLeft = container.scrollLeft;
        };

        const handleMouseUp = () => {
            isDown = false;
            container.classList.remove('scrolling-active');
        };

        const handleMouseLeave = () => {
            isDown = false;
            container.classList.remove('scrolling-active');
        };

        const handleMouseMove = (e: MouseEvent) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - container.offsetLeft;
            const walk = (x - startX) * 2; // Scroll speed multiplier
            container.scrollLeft = scrollLeft - walk;
        };

        container.addEventListener('mousedown', handleMouseDown);
        container.addEventListener('mouseup', handleMouseUp);
        container.addEventListener('mouseleave', handleMouseLeave);
        container.addEventListener('mousemove', handleMouseMove);

        return () => {
            container.removeEventListener('mousedown', handleMouseDown);
            container.removeEventListener('mouseup', handleMouseUp);
            container.removeEventListener('mouseleave', handleMouseLeave);
            container.removeEventListener('mousemove', handleMouseMove);
        };
    }, []);

    const handleDownloadDetails = async () => {
        setIsDownloading(true);
        try {
            const response = await fetch('/api/download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(appDetails),
            });

            if (!response.ok) {
                throw new Error('Failed to generate CSV');
            }

            // Get the CSV content
            const csvContent = await response.text();

            // Create a Blob from the CSV content
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

            // Create a download link
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${appName.replace(/[^a-zA-Z0-9]/g, '_')}_details.csv`;

            // Trigger the download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast.success('Download completed');
        } catch (error) {
            console.error('Error downloading details:', error);
            toast.error('Failed to download app details');
        } finally {
            setIsDownloading(false);
        }
    };

    return (
        <div className="bg-[#1a1b26] min-h-screen text-white">
            {/* App Hero Section */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 relative">
                <div className="mx-auto px-6 py-6 pb-16 pt-5 relative z-10">
                    <div className="flex flex-col items-start">
                        {/* App Name & Developer */}
                        <h1 className="text-3xl font-bold text-white mb-0">{appName}</h1>
                        <p className="text-white/75 mt-0 mb-3">{developer}</p>

                        {/* Ratings */}
                        {rating !== null && (
                            <div className="flex items-center mt-1 mb-4">
                                <div className="flex">
                                    {[1, 2, 3, 4, 5].map((_, i) => (
                                        <Star
                                            key={i}
                                            size={18}
                                            className="text-yellow-400 mr-0.5"
                                            fill="currentColor"
                                        />
                                    ))}
                                </div>
                                <span className="text-white font-medium ml-2">{rating.toFixed(1)}</span>
                                <span className="mx-2 text-white/75">•</span>
                                <span className="text-white/75">
                                    {ratingCount > 0
                                        ? `${ratingCount.toLocaleString()} reviews`
                                        : 'No reviews yet'}
                                </span>
                            </div>
                        )}

                        {/* App Stats */}
                        <div className="flex items-center gap-6 mt-4">
                            {rating !== null && (
                                <div className="flex items-center">
                                    <Star size={16} className="text-yellow-400 mr-1" fill="currentColor" />
                                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        {rating.toFixed(1)} ({ratingCount.toLocaleString()} ratings)
                                    </span>
                                </div>
                            )}
                        </div>

                        {/* Action Button */}
                        <a
                            href={appUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-4 py-2 bg-white text-blue-700 rounded-md text-sm font-medium hover:bg-white/90 transition-colors"
                        >
                            <Download size={16} className="mr-2" />
                            View on Play Store
                        </a>
                    </div>
                </div>

                {/* App Icon (positioned at bottom) */}
                <div className="absolute -bottom-8 left-6 z-10 w-16 h-16 rounded-xl overflow-hidden border-2 border-gray-700 bg-[#1E293B]">
                    {icon && !iconError ? (
                        <div className="relative w-full h-full">
                            <Image
                                src={getProxiedImageUrl(icon)}
                                alt={appName}
                                fill
                                className="object-contain"
                                sizes="64px"
                                priority
                                onError={() => setIconError(true)}
                            />
                        </div>
                    ) : (
                        <div className="w-full h-full flex items-center justify-center bg-[#1E293B]">
                            <Smartphone size={24} className="text-gray-400" />
                        </div>
                    )}
                </div>
            </div>

            {/* Main Content Area */}
            <div className="max-w-screen-xl mx-auto px-6 pb-12 pt-14">
                <div className="flex flex-col md:flex-row gap-6">
                    {/* Left Column - Main Content */}
                    <div className="flex-1">
                        {/* Screenshots Section */}
                        {hasScreenshots && (
                            <div className="mb-8">
                                <div className="flex items-center mb-3">
                                    <ImageIcon size={18} className="text-blue-400 mr-2" />
                                    <h3 className="text-lg font-semibold text-white">Screenshots</h3>
                                </div>
                                <div className="relative group">
                                    {/* Fixed width container to show exactly 3 screenshots */}
                                    <div className="w-full overflow-hidden">
                                        <div
                                            className="overflow-x-auto scrollbar-hide cursor-grab active:cursor-grabbing overscroll-x-contain"
                                            id="screenshot-scroll-container"
                                            ref={scrollContainerRef}
                                            style={{
                                                width: '100%',
                                                display: 'flex',
                                            }}
                                        >
                                            <div className="flex gap-3 pb-2">
                                                {validScreenshots.map((img, index) => (
                                                    <div
                                                        key={index}
                                                        className="flex-none relative rounded-lg overflow-hidden cursor-pointer bg-[#1E293B]"
                                                        style={{
                                                            width: 180,
                                                            height: 320,
                                                        }}
                                                        onClick={() => !imageErrors[index] && openFullView(img, index)}
                                                    >
                                                        {!imageErrors[index] ? (
                                                            <Image
                                                                src={getProxiedImageUrl(img)}
                                                                alt={`${appName} screenshot ${index + 1}`}
                                                                fill
                                                                className="object-cover"
                                                                unoptimized={true}
                                                                onError={() => handleImageError(index)}
                                                                priority={index < 3}
                                                            />
                                                        ) : (
                                                            <div className="w-full h-full flex flex-col items-center justify-center">
                                                                <ImageIcon size={24} className="text-gray-500 mb-1" />
                                                                <p className="text-gray-400 text-xs text-center px-2">Failed to load</p>
                                                            </div>
                                                        )}
                                                        <div className="absolute bottom-2 right-2 bg-black/40 text-white text-xs rounded-full px-2 py-0.5">
                                                            {index + 1}/{validScreenshots.length}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Navigation arrows - always visible */}
                                    {validScreenshots.length > 3 && (
                                        <>
                                            <button
                                                className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full p-2 shadow-md flex items-center justify-center text-white z-10"
                                                onClick={handleScrollLeft}
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                                </svg>
                                            </button>
                                            <button
                                                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full p-2 shadow-md flex items-center justify-center text-white z-10"
                                                onClick={handleScrollRight}
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                                </svg>
                                            </button>
                                        </>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Description Section */}
                        <div className="mb-6">
                            <div className="flex items-center mb-3">
                                <Info size={18} className="text-blue-400 mr-2" />
                                <h3 className="text-lg font-semibold text-white">Description</h3>
                            </div>
                            <div className="bg-[#1E293B] rounded-xl p-5">
                                <div className="prose prose-invert max-w-none text-gray-300 text-sm sm:text-base">
                                    {description && description.trim() && description !== 'No description available.' ? (
                                        showFullDescription || description.length <= 300 ? (
                                            <p className="whitespace-pre-line leading-relaxed">{description}</p>
                                        ) : (
                                            <>
                                                <p className="whitespace-pre-line leading-relaxed">{shortDescription}</p>
                                                <button
                                                    onClick={() => setShowFullDescription(true)}
                                                    className="text-blue-400 hover:underline mt-3 font-medium text-sm flex items-center"
                                                >
                                                    <span>Read more</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                                    </svg>
                                                </button>
                                            </>
                                        )
                                    ) : (
                                        <div className="flex flex-col items-center justify-center py-6">
                                            <Info size={32} className="text-gray-500 mb-2" />
                                            <span className="text-gray-400">No description available.</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {hasAdditionalInfo && (
                            <div className="mb-6">
                                <div className="flex items-center mb-3">
                                    <CalendarIcon size={18} className="text-blue-400 mr-2" />
                                    <h3 className="text-lg font-semibold text-white">Additional Information</h3>
                                </div>
                                <div className="bg-[#1E293B] rounded-xl p-5">
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                                        {Object.entries(additionalInfo).map(([key, value], index) => (
                                            <div key={index} className="flex flex-col">
                                                <span className="text-xs font-medium text-gray-400 mb-1">{key}</span>
                                                <span className="text-sm text-gray-200 break-words">{value}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Right Column - Category and Support */}
                    <div className="md:w-72 flex-shrink-0 space-y-4">
                        {/* Download Details Button */}
                        <div className="bg-[#1E293B] rounded-xl p-5">
                            <button
                                onClick={handleDownloadDetails}
                                className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={isDownloading}
                            >
                                {isDownloading ? (
                                    <>
                                        <Loader2 size={16} className="mr-2 animate-spin" />
                                        Downloading...
                                    </>
                                ) : (
                                    <>
                                        <FileDown size={16} className="mr-2" />
                                        Download Details
                                    </>
                                )}
                            </button>
                        </div>

                        {hasSupport && (
                            <div className="bg-[#1E293B] rounded-xl p-5">
                                <div className="flex items-center mb-3">
                                    <User size={18} className="text-blue-400 mr-2" />
                                    <h3 className="text-base font-semibold text-white">App Support</h3>
                                </div>
                                <div className="space-y-4">
                                    {supportEmail && (
                                        <div>
                                            <span className="block text-gray-400 text-xs font-medium mb-1">Email:</span>
                                            <a href={`mailto:${supportEmail}`} className="text-blue-400 hover:underline text-sm break-all">
                                                {supportEmail}
                                            </a>
                                        </div>
                                    )}
                                    {developerWebsite && (
                                        <div>
                                            <span className="block text-gray-400 text-xs font-medium mb-1">Website:</span>
                                            <a href={developerWebsite} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline text-sm break-all">
                                                {developerWebsite}
                                            </a>
                                        </div>
                                    )}
                                    {developerAddress && (
                                        <div>
                                            <span className="block text-gray-400 text-xs font-medium mb-1">Address:</span>
                                            <span className="text-sm text-gray-300 block">{developerAddress}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* About the Developer Section */}
                        <div className="bg-[#1E293B] rounded-xl p-5 mt-4">
                            <div className="flex items-center mb-3">
                                <Award size={18} className="text-blue-400 mr-2" />
                                <h3 className="text-base font-semibold text-white">About the Developer</h3>
                            </div>
                            <div className="space-y-4">
                                <div>
                                    <span className="block text-gray-400 text-xs font-medium mb-1">Developer:</span>
                                    <span className="text-sm text-gray-300 block">{developer}</span>
                                </div>
                                {developerWebsite && (
                                    <div>
                                        <span className="block text-gray-400 text-xs font-medium mb-1">Official Website:</span>
                                        <a href={developerWebsite} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline text-sm break-all">
                                            {developerWebsite}
                                        </a>
                                    </div>
                                )}
                                <div>
                                    <span className="block text-gray-400 text-xs font-medium mb-1">Apps on Play Store:</span>
                                    <a href={`https://play.google.com/store/apps/developer?id=${encodeURIComponent(developer)}`} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline text-sm flex items-center">
                                        View all apps
                                        <ExternalLink size={14} className="ml-1" />
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Footer Section */}
            <div className="bg-[#1a1b26] border-t border-gray-800">
                <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4 text-center text-xs sm:text-sm text-gray-500">
                    <p>App data sourced from Google Play Store. Last updated: {new Date().toLocaleDateString()}</p>
                    <p className="mt-1 text-xs text-gray-500">This tool is for educational purposes only.</p>
                </div>
            </div>

            {/* Fullscreen Modal for Screenshots */}
            {fullViewImage && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90" onClick={closeFullView}>
                    <div className="relative w-full h-full max-w-5xl max-h-[90vh] flex items-center justify-center p-4">
                        <button
                            className="absolute top-4 right-4 text-white bg-black/50 rounded-full p-2 hover:bg-black/70 transition-colors z-10"
                            onClick={closeFullView}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                        <div className="relative w-full h-full flex items-center justify-center">
                            <Image
                                src={getProxiedImageUrl(fullViewImage)}
                                alt="Screenshot full view"
                                fill
                                className="object-contain"
                                sizes="100vw"
                                unoptimized={true}
                                priority={true}
                            />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
} 