import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(request: NextRequest) {
    const url = request.nextUrl.searchParams.get('url');

    if (!url) {
        return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 });
    }

    try {
        // Only allow proxying Google's content domains for security
        if (!url.includes('googleusercontent.com') && !url.includes('play-lh.googleusercontent.com') && !url.includes('play.google.com')) {
            return NextResponse.json({ error: 'Only Google Play Store image URLs are allowed' }, { status: 403 });
        }

        const response = await axios.get(url, {
            responseType: 'arraybuffer',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://play.google.com/'
            }
        });

        // Determine the content type based on the URL extension or response headers
        let contentType = response.headers['content-type'];
        if (!contentType) {
            if (url.endsWith('.png')) contentType = 'image/png';
            else if (url.endsWith('.jpg') || url.endsWith('.jpeg')) contentType = 'image/jpeg';
            else if (url.endsWith('.webp')) contentType = 'image/webp';
            else contentType = 'image/jpeg'; // Default
        }

        // Return the image with appropriate headers
        return new NextResponse(response.data, {
            headers: {
                'Content-Type': contentType,
                'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
                'Access-Control-Allow-Origin': '*'
            }
        });

    } catch (error) {
        console.error('Error proxying image:', error);
        return NextResponse.json({ error: 'Failed to proxy image' }, { status: 500 });
    }
} 