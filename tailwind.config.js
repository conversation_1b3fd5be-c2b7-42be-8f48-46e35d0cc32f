/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        './pages/**/*.{js,ts,jsx,tsx,mdx}',
        './components/**/*.{js,ts,jsx,tsx,mdx}',
        './app/**/*.{js,ts,jsx,tsx,mdx}',
    ],
    theme: {
        extend: {
            colors: {
                primary: '#4285F4',
                secondary: '#34A853',
                accent: '#FBBC05',
                danger: '#EA4335',
                'base-100': '#ffffff',
                'base-200': '#f8f9fa',
                'base-300': '#e9ecef',
            },
        },
    },
    plugins: [require('daisyui')],
    daisyui: {
        themes: [
            {
                light: {
                    primary: '#4285F4',
                    secondary: '#34A853',
                    accent: '#FBBC05',
                    neutral: '#191D24',
                    'base-100': '#FFFFFF',
                    info: '#3ABFF8',
                    success: '#36D399',
                    warning: '#FBBD23',
                    error: '#EA4335',
                },
                dark: {
                    primary: '#4285F4',
                    secondary: '#34A853',
                    accent: '#FBBC05',
                    neutral: '#191D24',
                    'base-100': '#121212',
                    'base-200': '#1E1E1E',
                    'base-300': '#292929',
                    info: '#3ABFF8',
                    success: '#36D399',
                    warning: '#FBBD23',
                    error: '#EA4335',
                },
            },
        ],
    },
} 