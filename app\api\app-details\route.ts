import { NextResponse } from 'next/server';
import axios from 'axios';
import * as cheerio from 'cheerio';

export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const appId = searchParams.get('appId');

    if (!appId) {
        return NextResponse.json({ error: 'App ID parameter is required' }, { status: 400 });
    }

    try {
        const url = `https://play.google.com/store/apps/details?id=${appId}`;
        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept-Language': 'en-US,en;q=0.9',
            }
        });

        const $ = cheerio.load(response.data);
        console.log('Loaded app details page for:', appId);

        // Try different selectors for app name
        let appName = '';
        const nameSelectors = [
            'h1.AHFaub span',
            'h1[itemprop="name"] span',
            'h1.Fd93Bb span',
            'h1 span',
            'div.ooJXfd span',
            'div.Vbfug span',
            'div.qZmL0 div[role="heading"]',
            'div[role="heading"]',
            'div.xwY9Zc',
            'h1.b8cIId',
            'div.b8cIId span',
            'div.WsMG1c',
            'span.DdYX5'
        ];

        for (const selector of nameSelectors) {
            const nameElements = $(selector);
            nameElements.each((_, item) => {
                const nameText = $(item).text().trim();
                // Enhanced validation to avoid false positives
                if (nameText &&
                    nameText.length > 1 &&
                    nameText.length < 100 &&
                    !nameText.match(/^\d+(\.\d+)?star$/i) &&
                    !nameText.match(/^(free|paid|top|new|trending)$/i) &&
                    !nameText.match(/^(install|download|update|open)$/i) &&
                    !nameText.includes('rating') &&
                    !nameText.includes('reviews')) {
                    appName = nameText;
                    return false; // Break the each loop
                }
            });
            if (appName) {
                console.log(`Found app name using selector: ${selector}`);
                break;
            }
        }

        // If still no app name found, try parent elements
        if (!appName) {
            console.log('Trying parent elements for app name');
            const parentElements = $('div[role="heading"], h1, h2').first();
            const parentText = parentElements.text().trim();
            if (parentText &&
                parentText.length > 1 &&
                parentText.length < 100 &&
                !parentText.match(/^\d+(\.\d+)?star$/i)) {
                appName = parentText;
                console.log('Found app name from parent element');
            }
        }

        // Fallback: Use app ID if name not found
        if (!appName) {
            console.log('Using app ID as name fallback');
            appName = appId.split('.').pop() || appId;
        }

        // Try different selectors for developer
        let developer = '';
        const developerSelectors = [
            '.hrTbp.R8zArc',
            '[itemprop="author"] span',
            'a[href*="dev="]',
            'div.Vbfug',
            'span.T32cc'
        ];

        for (const selector of developerSelectors) {
            developer = $(selector).text().trim();
            if (developer) {
                console.log(`Found developer using selector: ${selector}`);
                break;
            }
        }

        // Try different selectors for description
        let description = '';
        const descriptionSelectors = [
            '.bARER',
            '[itemprop="description"] div',
            'div[jsname="sngebd"]',
            'div.W4P4ne'
        ];

        for (const selector of descriptionSelectors) {
            description = $(selector).text().trim();
            if (description) {
                console.log(`Found description using selector: ${selector}`);
                break;
            }
        }

        // Extract rating
        const ratingText = $('[aria-label*="rating"]').first().text().trim();
        const ratingMatch = ratingText.match(/(\d+\.\d+)/);
        const rating = ratingMatch ? parseFloat(ratingMatch[1]) : null;

        // Extract rating count
        const ratingCountText = $('[aria-label*="rating"]').next().text().trim();
        const ratingCount = parseInt(ratingCountText.replace(/[^0-9]/g, '')) || 0;

        // Extract installs
        let installs: number | undefined;

        // First try to find the downloads text that matches the pattern "X+ downloads" or "X+ installs"
        const downloadMatches = $('body').text().match(/(\d+(?:\.\d+)?[BbMmKk]?\+?)\s*(?:downloads|installs)/i);

        if (downloadMatches) {
            const downloadText = downloadMatches[1].toLowerCase();
            let multiplier = 1;
            let numericValue: number;

            // Handle special case for "10B+" format
            if (downloadText.match(/10b\+/i)) {
                installs = 10000000000; // 10 billion
            } else {
                if (downloadText.includes('b')) {
                    multiplier = 1000000000; // Billion
                } else if (downloadText.includes('m')) {
                    multiplier = 1000000; // Million
                } else if (downloadText.includes('k')) {
                    multiplier = 1000; // Thousand
                }

                numericValue = parseFloat(downloadText.replace(/[^0-9.]/g, ''));
                if (!isNaN(numericValue)) {
                    installs = numericValue * multiplier;
                }
            }
        }

        // If still not found, try specific elements with more precise selectors
        if (!installs) {
            const downloadSelectors = [
                'div[aria-label*="downloads"]',
                'span[aria-label*="downloads"]',
                'div[aria-label*="installs"]',
                'span[aria-label*="installs"]',
                'div.ClM7O',
                'div.wVqUob',
                'div.EGFGHd',
                'span.htlgb',
                'div[itemprop="numDownloads"]',
                // Additional selectors for different Play Store layouts
                'div.wVqUob div[class*="ClM7O"]',
                'div[jsname="K3EWx"]',
                'div[jsname="K3EWx"] span',
                'div[jsname="K3EWx"] div'
            ];

            for (const selector of downloadSelectors) {
                const element = $(selector);
                if (element.length) {
                    const text = element.text().toLowerCase().trim();

                    // Special case for "10B+"
                    if (text.match(/10b\+/i)) {
                        installs = 10000000000; // 10 billion
                        break;
                    }

                    // Handle other formats
                    if (text.includes('b+') || text.includes('billion')) {
                        const num = parseFloat(text.replace(/[^0-9.]/g, ''));
                        installs = num * 1000000000;
                        break;
                    } else if (text.includes('m+') || text.includes('million')) {
                        const num = parseFloat(text.replace(/[^0-9.]/g, ''));
                        installs = num * 1000000;
                        break;
                    } else if (text.includes('k+') || text.includes('thousand')) {
                        const num = parseFloat(text.replace(/[^0-9.]/g, ''));
                        installs = num * 1000;
                        break;
                    } else {
                        // Try to parse plain numbers
                        const num = parseInt(text.replace(/[^0-9]/g, ''));
                        if (!isNaN(num)) {
                            installs = num;
                            break;
                        }
                    }
                }
            }
        }

        // If still not found, try looking for the "10B+" format specifically in the entire body
        if (!installs) {
            const bodyText = $('body').text();
            if (bodyText.match(/10B\+/i)) {
                installs = 10000000000; // 10 billion
            }
        }

        // Try different selectors for images (screenshots only, broader)
        const screenshots: string[] = [];

        // Add more specific selectors for Play Store screenshots
        const screenshotContainers = [
            // Modern Play Store selectors
            'div.SgoUSc',
            'div.TdqJUe',
            'div[jsname="uLHQYe"]',
            'div[jsname="NOg0ud"]',
            'div.W4P4ne div.SgoUSc',
            // For the carousel/scroller component
            'c-wiz[jsrenderer="ZPIqHc"] div[jsaction*="click"]',
            // Generic containers that might hold screenshots
            '[data-screenshot-item-index]',
            'button[jsaction*="Screenshot"]',
            'div[data-screenshot-item-index]'
        ];

        // Check for screenshot containers first (more reliable)
        let foundInContainer = false;

        for (const containerSelector of screenshotContainers) {
            const containers = $(containerSelector);
            if (containers.length) {
                console.log(`Found screenshot container: ${containerSelector} with ${containers.length} items`);

                containers.each((_, container) => {
                    // Try to find images inside the container
                    $(container).find('img').each((_, img) => {
                        const src = $(img).attr('src') || $(img).attr('data-src');

                        if (src &&
                            // Only include images that look like screenshots
                            (src.includes('googleusercontent') || src.includes('play-lh')) &&
                            // Exclude small images (likely icons)
                            !src.includes('=w32-') &&
                            !src.includes('=w64-') &&
                            !src.includes('=s64-')) {

                            // Get high quality version if possible
                            const highQualitySrc = src.replace(/=w\d+-h\d+/, '=w2000-h1000')
                                .replace(/=s\d+/, '=s2000');

                            screenshots.push(highQualitySrc);
                            foundInContainer = true;
                        }
                    });
                });

                if (foundInContainer && screenshots.length > 0) {
                    console.log(`Successfully extracted ${screenshots.length} screenshots from container`);
                    break;
                }
            }
        }

        // If containers didn't work, try direct selectors
        if (!foundInContainer || screenshots.length === 0) {
            console.log('Trying direct image selectors for screenshots');

            const imageSelectors = [
                // Common screenshot dimensions in Play Store
                'img[src*="=w1080-h1920"]',
                'img[src*="=w1440-h2560"]',
                'img[src*="=w2560-h1440"]',
                'img[src*="=w1920-h1080"]',
                'img[src*="=w2000"]',
                'img[src*="=w1200"]',
                // More generic selectors
                'img[aria-label*="screenshot"]',
                'img[src*="play-lh.googleusercontent.com"]',
                'img[src*="googleusercontent"][srcset]',
                'img[src*="googleusercontent"]:not([src*="=w32-"]):not([src*="=w64-"])'
            ];

            for (const selector of imageSelectors) {
                $(selector).each((_, element) => {
                    const src = $(element).attr('src') || $(element).attr('data-src');
                    if (src && !screenshots.includes(src)) {
                        // Skip images that are too small (likely icons)
                        if (src.includes('=w32-') || src.includes('=w64-') || src.includes('=s64-')) {
                            return;
                        }

                        // Get high quality version if possible
                        const highQualitySrc = src.replace(/=w\d+-h\d+/, '=w2000-h1000')
                            .replace(/=s\d+/, '=s2000');

                        screenshots.push(highQualitySrc);
                    }
                });

                if (screenshots.length > 0) {
                    console.log(`Found ${screenshots.length} screenshots using selector: ${selector}`);
                    break;
                }
            }
        }

        // Special handling for srcset attribute which might contain higher quality images
        if (screenshots.length === 0) {
            console.log('Trying srcset attributes for screenshots');
            $('img[srcset]').each((_, element) => {
                const srcset = $(element).attr('srcset');
                if (srcset && (srcset.includes('googleusercontent') || srcset.includes('play-lh'))) {
                    // Extract highest resolution URL from srcset
                    const srcsetUrls = srcset.split(',').map(s => s.trim().split(' ')[0]);
                    const highestRes = srcsetUrls[srcsetUrls.length - 1]; // Last one is typically highest res

                    if (highestRes && !screenshots.includes(highestRes)) {
                        screenshots.push(highestRes);
                    }
                }
            });
        }

        // Try to detect if images are actually screenshots vs icons/promo images
        const filteredScreenshots = screenshots.filter(src => {
            // Exclude small images and icons
            if (src.includes('=w32-') || src.includes('=w64-') ||
                src.includes('=s32-') || src.includes('=s64-')) {
                return false;
            }

            // Likely to be screenshots if they:
            // 1. Have standard Play Store screenshot dimensions
            // 2. Come from Google's content servers
            // 3. Are large images (not icons)
            return (
                src.includes('googleusercontent') ||
                src.includes('play-lh') ||
                src.includes('=w1080-') ||
                src.includes('=w1440-') ||
                src.includes('=w2560-') ||
                src.includes('=w1920-') ||
                src.includes('=w2000-')
            );
        });

        // Apply additional filtering to remove unwanted/duplicate screenshots
        const finalScreenshots = [];
        const seenPatterns = new Set();

        // Make sure URLs are absolute (sometimes Play Store returns relative URLs)
        const normalizeImageUrl = (url: string) => {
            if (!url) return '';

            // If it's already an absolute URL, return as is
            if (url.startsWith('http')) return url;

            // If it's a protocol-relative URL, add https:
            if (url.startsWith('//')) return `https:${url}`;

            // Otherwise, assume it's relative to the Google Play domain
            return `https://play.google.com${url.startsWith('/') ? url : `/${url}`}`;
        };

        // Prioritize adding screenshots that look most like actual app screenshots
        for (const src of filteredScreenshots) {
            const normalizedUrl = normalizeImageUrl(src);

            // Skip empty URLs
            if (!normalizedUrl) continue;

            // Extract the base part of the URL to identify similar images
            const urlPattern = normalizedUrl.split('=')[0];

            // Skip if we've already added a similar image
            if (seenPatterns.has(urlPattern)) continue;

            // Skip likely icons (square images with sizes below 512px)
            if (src.includes('=s128') || src.includes('=s256') || src.includes('=s512')) {
                continue;
            }

            // Skip likely logos or small graphics
            if (src.includes('logo') || src.includes('icon') || src.includes('badge')) {
                continue;
            }

            // Give preference to actual screenshots with proper dimensions
            const isPreferredScreenshot =
                src.includes('=w1080-h1920') ||
                src.includes('=w1440-h2560') ||
                src.includes('=w2560-h1440') ||
                src.includes('=w1920-h1080');

            if (isPreferredScreenshot || finalScreenshots.length < 5) {
                finalScreenshots.push(normalizedUrl);
                seenPatterns.add(urlPattern);
            }

            // Limit to 5 screenshots maximum
            if (finalScreenshots.length >= 5) break;
        }

        console.log(`Selected ${finalScreenshots.length} high-quality screenshots for display`);

        // Try different selectors for icon
        let icon = '';
        const iconSelectors = [
            '.xSyT2c img',
            'img.T75of',
            'img.gAGblf',
            'img[src*="googleusercontent"][alt="Icon image"]',
            'img[alt*="icon"]'
        ];

        for (const selector of iconSelectors) {
            icon = $(selector).attr('src') || '';
            if (icon) {
                console.log(`Found icon using selector: ${selector}`);
                break;
            }
        }

        // Try different selectors for category
        let category = '';
        const categorySelectors = [
            '.ZVWMWc span',
            'a[href*="category"]',
            'span[itemprop="genre"]'
        ];
        for (const selector of categorySelectors) {
            const cat = $(selector).text().trim();
            if (cat && cat.length > 1 && cat.length < 50 && cat.toLowerCase() !== 'category') {
                category = cat;
                break;
            }
        }

        // Try to find additional information
        const additionalInfo: Record<string, string> = {};
        const infoSelectionPairs = [
            { titleSelector: '.BgcNfc', valueSelector: '.htlgb' },
            { titleSelector: 'div.zc7KVe', valueSelector: 'div.Ph9hCc' }
        ];

        for (const { titleSelector, valueSelector } of infoSelectionPairs) {
            let foundInfo = false;
            $(titleSelector).each((i, element) => {
                const infoTitle = $(element).text().trim();
                const infoValue = $(element).next(valueSelector).text().trim();
                if (infoTitle && infoValue) {
                    additionalInfo[infoTitle] = infoValue;
                    foundInfo = true;
                }
            });

            if (foundInfo) {
                console.log(`Found additional info using selectors: ${titleSelector}, ${valueSelector}`);
                break;
            }
        }

        // Extract support email and developer info
        let supportEmail = '';
        let developerWebsite = '';
        let developerAddress = '';
        // Play Store uses mailto: and http(s) links in developer section
        $('a[href^="mailto:"]').each((i, el) => {
            const email = $(el).attr('href')?.replace('mailto:', '').trim();
            if (email && !supportEmail) supportEmail = email;
        });
        $('a[href^="http"]').each((i, el) => {
            const href = $(el).attr('href');
            if (href && !developerWebsite && $(el).text().toLowerCase().includes('website')) {
                developerWebsite = href;
            }
        });
        // Address is sometimes in a div or span with address or location keywords
        $('div,span').each((i, el) => {
            const text = $(el).text().trim();
            if (text && text.length > 10 && text.match(/\d+\s+\w+\s+(street|road|ave|blvd|city|state|country|zip|pincode|address)/i)) {
                developerAddress = text;
            }
        });

        return NextResponse.json({
            appId,
            appName,
            developer,
            description,
            rating,
            ratingCount,
            screenshots: finalScreenshots,
            icon,
            category,
            additionalInfo,
            appUrl: url,
            supportEmail,
            developerWebsite,
            developerAddress,
            installs
        });
    } catch (error: unknown) {
        console.error('Error fetching app details:', error);

        // Just return a basic error with the app ID
        const appName = appId.split('.').pop() || appId;
        return NextResponse.json({
            appId,
            appName: appName.charAt(0).toUpperCase() + appName.slice(1),
            developer: 'Unknown Developer',
            description: 'No description available.',
            rating: null,
            ratingCount: 0,
            screenshots: [],
            icon: '',
            category: 'App',
            additionalInfo: {},
            appUrl: `https://play.google.com/store/apps/details?id=${appId}`,
            error: 'Failed to fetch app details from Google Play Store.'
        });
    }
} 