'use client';

import { Smartphone } from 'lucide-react';
import Link from 'next/link';
import ThemeToggle from './ThemeToggle';

export default function Navbar() {
    return (
        <nav>
            <div className="container mx-auto px-4">
                <div className="flex justify-between items-center h-16">
                    <Link href="/" className="flex items-center space-x-2">
                        <Smartphone size={24} className="text-primary" />
                        <span className="font-bold text-xl text-gray-900 dark:text-white">AppScraper</span>
                    </Link>

                    <div className="flex items-center space-x-4">
                        <Link
                            href="/"
                            className="text-gray-700 hover:text-primary dark:text-gray-300 dark:hover:text-white transition-colors"
                        >
                            Home
                        </Link>
                        {/* <ThemeToggle /> */}
                    </div>
                </div>
            </div>
        </nav>
    );
} 