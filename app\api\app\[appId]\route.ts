import { NextResponse } from 'next/server';
import axios from 'axios';
import * as cheerio from 'cheerio';

export async function GET(request: Request, { params }: { params: { appId: string } }) {
    try {
        const appId = params.appId;
        const url = `https://play.google.com/store/apps/details?id=${appId}&hl=en`;

        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept-Language': 'en-US,en;q=0.9',
            }
        });

        const $ = cheerio.load(response.data);

        // Extract app name
        const appName = $('h1[itemprop="name"], h1').first().text().trim();

        // Extract developer name
        const developer = $('[href*="developer?id="]').first().text().trim();

        // Extract description
        const description = $('[data-g-id="description"] div.SfzRHd, [itemprop="description"]').text().trim() || 'No description available.';

        // Extract rating
        const ratingText = $('[aria-label*="rating"]').first().text().trim();
        const ratingMatch = ratingText.match(/(\d+\.\d+)/);
        const rating = ratingMatch ? parseFloat(ratingMatch[1]) : null;

        // Extract rating count
        const ratingCountText = $('[aria-label*="rating"]').next().text().trim();
        const ratingCount = parseInt(ratingCountText.replace(/[^0-9]/g, '')) || 0;

        // Extract screenshots with better filtering
        const screenshots: string[] = [];
        $('img[src*="play-lh.googleusercontent"]').each((_, element) => {
            const src = $(element).attr('src');
            if (!src) return;

            // Skip promotional images, icons, and small images
            if (src.includes('rw-w64') ||
                src.includes('rw-w32') ||
                src.includes('rw-w48') ||
                src.includes('promotion')) {
                return;
            }

            // Extract dimensions from URL
            const dimensionsMatch = src.match(/=w(\d+)-h(\d+)/);
            if (dimensionsMatch) {
                const width = parseInt(dimensionsMatch[1]);
                const height = parseInt(dimensionsMatch[2]);

                // Only include images that are:
                // 1. Large enough (likely screenshots)
                // 2. Not square (likely not logos)
                // 3. Have a mobile-like aspect ratio
                if (width >= 320 && height >= 640 &&
                    width !== height &&
                    height > width) { // Mobile screenshots are typically portrait

                    // Get the highest quality version of the image
                    const highQualitySrc = src.replace(/=w\d+-h\d+/, '=w1440-h2960');
                    if (!screenshots.includes(highQualitySrc)) {
                        screenshots.push(highQualitySrc);
                    }
                }
            }
        });

        // Extract icon
        const icon = $('img[src*="play-lh.googleusercontent"][src*="rw-w64"]').first().attr('src') || '';

        // Extract category using multiple selectors and patterns
        let category = '';

        // Try finding category from breadcrumb or navigation
        $('a[href*="/store/apps/category/"], [itemprop="genre"], a[href*="category/"]').each((_, element) => {
            const text = $(element).text().trim();
            const href = $(element).attr('href') || '';

            // Validate if it's a real category
            if (text &&
                text.length > 1 &&
                !text.includes('http') &&
                !text.includes('www') &&
                href.includes('category')) {
                category = text;
                return false; // Break the loop once found
            }
        });

        // If still no category, try additional info section
        if (!category) {
            $('div.pSEeg, div[class*="details"], div[class*="info"]').each((_, element) => {
                const label = $(element).find('div').first().text().trim();
                const value = $(element).find('div').last().text().trim();

                if (label.toLowerCase().includes('category') ||
                    label.toLowerCase().includes('genre')) {
                    category = value;
                    return false;
                }
            });
        }

        // Extract additional information
        const additionalInfo: Record<string, string> = {};
        $('div.pSEeg, div[class*="details"], div[class*="info"]').each((_, element) => {
            const label = $(element).find('div').first().text().trim();
            const value = $(element).find('div').last().text().trim();

            if (label && value && label !== value) {
                additionalInfo[label] = value;
            }
        });

        // Extract support information
        const supportEmail = $('a[href^="mailto:"]').attr('href')?.replace('mailto:', '') || '';

        // Extract developer website
        let developerWebsite = '';
        $('a[href*="://"]').each((_, element) => {
            const href = $(element).attr('href') || '';
            if (!href.includes('play.google.com') &&
                !href.includes('mailto:') &&
                !href.includes('javascript:') &&
                href.match(/^https?:\/\//)) {
                developerWebsite = href;
                return false;
            }
        });

        // Extract developer address
        let developerAddress = '';
        $('div').each((_, element) => {
            const text = $(element).text().trim();
            if (text.match(/^[A-Za-z0-9\s,.]+(?:Street|Road|Ave|Blvd|Ln|Dr|Ct|Plaza|Square|Hwy|Building|Floor|Suite|Room|Unit|Box|District|City|State|Province|County|Country|Postal Code|ZIP)/i)) {
                developerAddress = text;
                return false;
            }
        });

        return NextResponse.json({
            appId,
            appName,
            developer,
            description,
            rating,
            ratingCount,
            screenshots,
            icon,
            category: category || 'Unknown',
            additionalInfo,
            appUrl: url,
            supportEmail,
            developerWebsite,
            developerAddress
        });

    } catch (error) {
        console.error('Error fetching app details:', error);
        return NextResponse.json({ error: 'Failed to fetch app details' }, { status: 500 });
    }
} 