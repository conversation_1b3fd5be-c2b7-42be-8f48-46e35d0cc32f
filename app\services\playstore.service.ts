import axios from 'axios';
import * as cheerio from 'cheerio';

export interface PlayStoreApp {
    packageName: string;
    title: string;
    developer: string;
    description: string;
    icon: string;
    category: string;
    rating: number;
    reviews: number;
    installs: string;
    price: string;
    lastUpdated: string;
    size: string;
    androidVersion: string;
}

export class PlayStoreService {
    private readonly baseUrl = 'https://play.google.com';
    private readonly searchUrl = 'https://play.google.com/store/search';

    constructor() {
        console.log('PlayStoreService initialized');
    }

    async searchApps(query: string, options: {
        language?: string;
        country?: string;
        maxResults?: number;
        category?: string;
    } = {}): Promise<PlayStoreApp[]> {
        try {
            console.log('Searching apps with query:', query, 'and options:', options);

            const {
                language = 'en',
                country = 'US',
                maxResults = 100,
                category
            } = options;

            let allApps: PlayStoreApp[] = [];
            let page = 0;

            while (allApps.length < maxResults) {
                const url = this.constructSearchUrl(query, category, page);
                console.log('Fetching URL:', url);

                const response = await axios.get(url, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept-Language': `${language}-${country},${language};q=0.9`,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache',
                        'Referer': 'https://play.google.com/',
                        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120"',
                        'sec-ch-ua-mobile': '?0',
                        'sec-ch-ua-platform': '"Windows"',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1',
                        'Cookie': 'AEC=Ackid1QhWqww4Yl_Hs1yXtTN4iCr0dqz6-dOF6A7RwQxNQ0tYw; NID=511=eKB77Y1dAH1YXzGz6Y6y7EDVYw'
                    }
                });

                const newApps = await this.parseSearchResults(response.data);
                console.log(`Found ${newApps.length} apps on page ${page + 1}`);

                if (newApps.length === 0) {
                    console.log('No more apps found');
                    break;
                }

                // Filter out duplicates
                const uniqueNewApps = newApps.filter(newApp =>
                    !allApps.some(existingApp => existingApp.packageName === newApp.packageName)
                );

                if (uniqueNewApps.length === 0) {
                    console.log('No new unique apps found');
                    break;
                }

                console.log(`Adding ${uniqueNewApps.length} unique new apps`);
                allApps = [...allApps, ...uniqueNewApps];

                if (allApps.length >= maxResults) {
                    console.log(`Reached desired number of apps (${maxResults})`);
                    break;
                }

                // Add delay between requests
                await new Promise(resolve => setTimeout(resolve, 2000));
                page++;

                // Safety check - don't go beyond 10 pages
                if (page >= 10) {
                    console.log('Reached maximum page limit');
                    break;
                }
            }

            console.log(`Total unique apps found: ${allApps.length}`);
            return allApps.slice(0, maxResults);
        } catch (error) {
            console.error('Error searching apps:', error);
            throw error;
        }
    }

    private constructSearchUrl(query: string, category?: string, page: number = 0): string {
        const params = new URLSearchParams();
        params.append('q', query);
        params.append('c', 'apps');
        params.append('hl', 'en'); // Language
        params.append('gl', 'US'); // Country
        params.append('showAllResults', 'true');

        if (category && category !== 'ALL') {
            params.append('category', category);
        }

        // Add pagination parameters
        if (page > 0) {
            params.append('start', (page * 48).toString());
            params.append('num', '48');
        }

        const url = `${this.searchUrl}?${params.toString()}`;
        console.log('Constructed URL:', url);
        return url;
    }

    private parseSearchResults(html: string): PlayStoreApp[] {
        const $ = cheerio.load(html);
        const apps: PlayStoreApp[] = [];
        const seenPackageNames = new Set<string>();

        // Debug HTML content
        console.log('HTML Content Length:', html.length);
        console.log('Sample HTML:', html.substring(0, 500));

        // Try multiple selectors to find app elements
        const selectors = [
            'div[data-uitype="500"]',
            'c-wiz[jsrenderer] div.VfPpkd-aGsRMb',
            'div.ULeU3b',
            'div[jscontroller][jsaction*="click"]',
            'a[href*="/store/apps/details?id="]',
            'div.mpYwdb',
            'div.Vpfmgd'
        ];

        for (const selector of selectors) {
            console.log(`Trying selector: ${selector}`);
            const elements = $(selector);
            console.log(`Found ${elements.length} elements with selector: ${selector}`);

            elements.each((_, element) => {
                try {
                    const el = $(element);

                    // Try to find the package name
                    let packageName = '';
                    const href = el.attr('href') || el.find('a[href*="details?id="]').attr('href') || '';
                    const packageMatch = href.match(/[?&]id=([^&]+)/);
                    if (packageMatch) {
                        packageName = packageMatch[1];
                    }

                    if (!packageName || seenPackageNames.has(packageName)) {
                        return;
                    }

                    seenPackageNames.add(packageName);

                    // Try multiple selectors for each field
                    const titleSelectors = [
                        'span.DdYX5',
                        'div.WsMG1c',
                        'div[role="heading"]',
                        'span[role="heading"]',
                        'div.b8cIId span',
                        'div.vWM94c',
                        'span.sT93pb'
                    ];

                    const developerSelectors = [
                        'div.KoLSrc',
                        'div[class*="developer"]',
                        'span.wMUdtb',
                        'div.LbQbAe',
                        'div.b8cIId div:nth-child(2)'
                    ];

                    const title = el.find(titleSelectors.join(', ')).first().text().trim();
                    const developer = el.find(developerSelectors.join(', ')).first().text().trim();

                    let rating = 0;
                    const ratingEl = el.find('[aria-label*="Rated"], [aria-label*="rating"], div.pf5lIe');
                    ratingEl.each((_, rEl) => {
                        const ratingText = $(rEl).attr('aria-label') || $(rEl).text();
                        if (ratingText) {
                            const ratingMatch = ratingText.match(/(\d+(\.\d+)?)/);
                            if (ratingMatch) {
                                rating = parseFloat(ratingMatch[1]);
                            }
                        }
                    });

                    const icon = el.find('img[src*="googleusercontent"], img[srcset*="googleusercontent"]').first().attr('src') || '';

                    if (title && packageName) {
                        apps.push({
                            packageName,
                            title,
                            developer: developer || 'Unknown Developer',
                            description: '',
                            icon,
                            category: '',
                            rating,
                            reviews: 0,
                            installs: '',
                            price: 'Free',
                            lastUpdated: '',
                            size: '',
                            androidVersion: ''
                        });
                    }
                } catch (error) {
                    console.error('Error parsing app element:', error);
                }
            });

            if (apps.length > 0) {
                console.log(`Successfully found ${apps.length} apps with selector: ${selector}`);
                break;
            }
        }

        console.log(`Total apps parsed: ${apps.length}`);
        return apps;
    }

    async getTopApps(category?: string, options: {
        language?: string;
        country?: string;
        maxResults?: number;
    } = {}): Promise<PlayStoreApp[]> {
        try {
            const {
                language = 'en',
                country = 'US',
                maxResults = 100
            } = options;

            const query = category ? `category:${category}` : 'top free';
            return this.searchApps(query, { language, country, maxResults });
        } catch (error) {
            console.error('Error fetching top apps:', error);
            throw error;
        }
    }

    async getSimilarApps(packageName: string, maxResults: number = 10): Promise<PlayStoreApp[]> {
        try {
            const query = `similar:${packageName}`;
            return this.searchApps(query, { maxResults });
        } catch (error) {
            console.error('Error fetching similar apps:', error);
            throw error;
        }
    }
} 