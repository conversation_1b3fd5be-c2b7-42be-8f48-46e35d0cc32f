# App Scraper - Google Play Store App Search

A modern web application for searching and viewing detailed information about Android apps from the Google Play Store. Built with Next.js, React, and Tailwind CSS.

## Features

- 🔍 Search for apps from the Google Play Store
- 📱 View app details including description, screenshots, and ratings
- 🌙 Dark mode support
- 📊 Responsive design for all devices
- ⚡ Fast and easy to use interface

## Tech Stack

- **Framework**: Next.js 14
- **UI Library**: React 19
- **Styling**: Tailwind CSS
- **UI Components**: DaisyUI
- **Icons**: Lucide React
- **Scraping**: Axios + Cheerio
- **Notifications**: React Hot Toast
- **Theming**: Next-themes

## Getting Started

### Prerequisites

- Node.js 18.0 or later
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/app-scraper.git
   cd app-scraper
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
   ```

4. Open your browser and navigate to [http://localhost:3000](http://localhost:3000)

## Usage

1. Enter the name of an app you want to search for in the search bar
2. Browse through the search results
3. Click on any app to view detailed information
4. Toggle between light and dark mode using the theme button in the navbar

## Disclaimer

This application is for educational purposes only. The data is scraped from the Google Play Store and may be subject to Google's terms of service. Use it responsibly and at your own risk.

## License

MIT
