import { NextResponse } from 'next/server';
import axios from 'axios';
import * as cheerio from 'cheerio';
import { CheerioAPI } from 'cheerio';

interface AppData {
    appName: string;
    developer: string;
    rating: number | null;
    category: string;
    appId: string;
    appUrl: string;
    supportEmail: string;
    developerWebsite: string;
}

export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '200');
    const category = searchParams.get('category') || 'ALL';

    try {
        // Calculate pagination for Google Play Store
        const appsPerGooglePage = 48;
        const startGooglePage = Math.floor(((page - 1) * limit) / appsPerGooglePage);
        const endGooglePage = Math.ceil((page * limit) / appsPerGooglePage);
        const googlePagesToFetch = endGooglePage - startGooglePage;

        const allApps: AppData[] = [];

        // For ALL category without query, fetch top apps from each category
        if (category === 'ALL' && !query) {
            const popularCategories = [
                'GAME', 'SOCIAL', 'PRODUCTIVITY', 'EDUCATION',
                'ENTERTAINMENT', 'COMMUNICATION', 'TOOLS', 'SHOPPING'
            ];

            for (const cat of popularCategories) {
                const url = `https://play.google.com/store/apps/category/${cat}?start=${startGooglePage * appsPerGooglePage}`;
                const response = await axios.get(url, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept-Language': 'en-US,en;q=0.9',
                    }
                });

                const $ = cheerio.load(response.data);
                await processAppElements($, allApps);

                // Limit to first 25 apps per category for current page
                if (allApps.length >= popularCategories.length * 25) {
                    break;
                }
            }
        } else {
            // Fetch apps from Google Play Store for specific category or search
            for (let i = 0; i < googlePagesToFetch; i++) {
                const googlePageNum = startGooglePage + i;
                const baseUrl = 'https://play.google.com/store/apps';
                let url;

                if (query) {
                    // Search with query
                    const searchUrl = 'https://play.google.com/store/search';
                    const categoryParam = category !== 'ALL' ? `&c=apps&category=${category}` : '&c=apps';
                    url = `${searchUrl}?q=${encodeURIComponent(query)}${categoryParam}&start=${googlePageNum * appsPerGooglePage}`;
                } else {
                    // Category-only browse
                    url = `${baseUrl}/category/${category}?start=${googlePageNum * appsPerGooglePage}`;
                }

                const response = await axios.get(url, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept-Language': 'en-US,en;q=0.9',
                    }
                });

                const $ = cheerio.load(response.data);
                await processAppElements($, allApps);
            }
        }

        // Calculate the correct slice for the current page
        const startIndex = (page - 1) * limit;
        const endIndex = Math.min(startIndex + limit, allApps.length);
        const paginatedApps = allApps.slice(startIndex, endIndex);

        // Generate CSV content with BOM for Excel compatibility
        const BOM = '\uFEFF';
        const csvHeader = 'App Name,Developer,Rating,Category,App ID,App URL,Support Email,Developer Website\n';
        const csvRows = paginatedApps.map(app => {
            return [
                `"${(app.appName || '').replace(/"/g, '""')}"`,
                `"${(app.developer || 'Unknown Developer').replace(/"/g, '""')}"`,
                app.rating || '',
                `"${(app.category || 'Unknown').replace(/"/g, '""')}"`,
                `"${app.appId}"`,
                `"${app.appUrl}"`,
                `"${(app.supportEmail || '').replace(/"/g, '""')}"`,
                `"${(app.developerWebsite || '').replace(/"/g, '""')}"`
            ].join(',');
        }).join('\n');

        const csvContent = BOM + csvHeader + csvRows;

        // Set filename based on search type
        const filename = query
            ? `app_search_${query.replace(/[^a-zA-Z0-9]/g, '_')}_page${page}.csv`
            : `${category.toLowerCase()}_apps_page${page}.csv`;

        // Return CSV file
        return new NextResponse(csvContent, {
            headers: {
                'Content-Type': 'text/csv; charset=utf-8',
                'Content-Disposition': `attachment; filename="${filename}"`,
            },
        });

    } catch (error) {
        console.error('Error generating CSV:', error);
        return NextResponse.json({ error: 'Failed to generate CSV' }, { status: 500 });
    }
}

// Helper function to process app elements
async function processAppElements($: CheerioAPI, allApps: AppData[]) {
    // Extract app data using various selectors
    const appPromises: Promise<AppData | null>[] = [];
    $('div[data-uitype="500"], c-wiz[data-node-index] div.VfPpkd-aGsRMb, div.ULeU3b, div.Vpfmgd').each((_, element) => {
        try {
            const el = $(element);

            // Helper function to fetch app details
            async function fetchAppDetails(appId: string): Promise<string | null> {
                try {
                    const detailUrl = `https://play.google.com/store/apps/details?id=${appId}&hl=en`;
                    const detailResponse = await axios.get(detailUrl, {
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                            'Accept-Language': 'en-US,en;q=0.9',
                        }
                    });

                    const $detail = cheerio.load(detailResponse.data);
                    const detailNameSelectors = [
                        'h1[itemprop="name"]',
                        'h1.AHFaub',
                        'h1.Fd93Bb',
                        'div.ooJXfd span',
                        'div.Vbfug span'
                    ];

                    for (const selector of detailNameSelectors) {
                        const detailName = $detail(selector).first().text().trim();
                        if (detailName &&
                            detailName.length > 1 &&
                            !detailName.match(/^\d+(\.\d+)?star$/i) &&
                            !detailName.match(/^[\.]+$/)) {
                            return detailName;
                        }
                    }
                } catch (error) {
                    console.error(`Error fetching app details for name: ${error}`);
                }
                return null;
            }

            // Enhanced app name extraction with better validation
            let appName = '';
            let developer = '';
            let rating: number | null = null;
            let category = '';
            let appId = '';

            // First extract app ID to help validate app name
            const appLink = el.find('a[href*="details?id="]').attr('href') || '';
            const appIdMatch = appLink.match(/id=([^&]+)/);
            if (appIdMatch) {
                appId = appIdMatch[1];
            }

            // Extract app name with strict validation
            const nameSelectors = [
                'div.WsMG1c',
                'div.b8cIId span',
                'span.DdYX5',
                'div.vWM94c',
                'span.sT93qe',
                'div.poRVub',
                'a[href*="details?id="] span',
                'a[href*="details?id="] div',
                'h1[itemprop="name"]',
                'h1.AHFaub span',
                'h1.Fd93Bb span',
                'div.ooJXfd span',
                'div.Vbfug span',
                'div.qZmL0 div[role="heading"]',
                'div[role="heading"]',
                'div.xwY9Zc',
                'div.Vbfug > div:first-child',
                'div.b8cIId > div:first-child'
            ];

            // First pass: Try to find app name with strict validation
            for (const selector of nameSelectors) {
                const nameElements = el.find(selector);
                for (let i = 0; i < nameElements.length; i++) {
                    const item = nameElements[i];
                    const nameText = $(item).text().trim();
                    // Strict validation to avoid false positives
                    if (nameText &&
                        nameText.length > 1 &&
                        nameText.length < 100 &&
                        !nameText.match(/^\d+(\.\d+)?$/) &&
                        !nameText.match(/^\d+(\.\d+)?star$/i) &&
                        !nameText.match(/^(free|paid|top|new|trending)$/i) &&
                        !nameText.match(/^(install|download|update|open)$/i) &&
                        !nameText.match(/^[\.]+$/) &&  // Prevent dots
                        !nameText.includes('rating') &&
                        !nameText.includes('reviews') &&
                        !nameText.includes('stars') &&
                        nameText !== '.' &&
                        nameText !== '..' &&
                        nameText !== '...') {
                        appName = nameText;
                        break;
                    }
                }
                if (appName) break;
            }

            // Extract rating with strict validation
            const ratingSelectors = [
                '[aria-label*="rating"]',
                '[aria-label*="Rated"]',
                '.TT9eCd',
                '.jILTFe'
            ];

            for (const selector of ratingSelectors) {
                const ratingEl = el.find(selector).first();
                const ratingText = ratingEl.text().trim();
                const ratingMatch = ratingText.match(/(\d+(\.\d+)?)/);
                if (ratingMatch) {
                    const parsedRating = parseFloat(ratingMatch[1]);
                    if (!isNaN(parsedRating) && parsedRating >= 0 && parsedRating <= 5) {
                        rating = parsedRating;
                        break;
                    }
                }
            }

            // Extract category with strict validation
            const categorySelectors = [
                'a[href*="/store/apps/category/"]',
                'span[itemprop="genre"]',
                'div[itemprop="genre"]',
                'a[href*="browse/"]'
            ];

            for (const selector of categorySelectors) {
                const categoryEl = el.find(selector).first();
                const categoryText = categoryEl.text().trim();
                if (categoryText &&
                    !categoryText.match(/^\d+(\.\d+)?star$/i) &&
                    !categoryText.match(/^\d+(\.\d+)?$/) &&
                    !categoryText.includes('rating') &&
                    !categoryText.includes('reviews') &&
                    categoryText !== appName) {
                    category = categoryText;
                    break;
                }
            }

            // Extract developer info with validation
            const developerSelectors = [
                'a[href*="developer/"]',
                'div[itemprop="author"]',
                '.b8cIId a',
                '.KoLSrc',
                '.pdqJpd'
            ];

            for (const selector of developerSelectors) {
                const devEl = el.find(selector).first();
                const devText = devEl.text().trim();
                if (devText &&
                    devText !== appName &&
                    devText !== category &&
                    !devText.match(/^\d+(\.\d+)?star$/i) &&
                    !devText.match(/^\d+(\.\d+)?$/)) {
                    developer = devText;
                    break;
                }
            }

            // If no valid app name found, try to extract from app ID
            if (!appName && appId) {
                appName = appId.split('.').pop() || appId;
            }

            // Create a promise for fetching detailed app info
            const detailPromise = (async (): Promise<AppData | null> => {
                try {
                    if (appId) {
                        // If no valid name found, try to get it from app details
                        if (!appName || appName === '.' || appName.match(/^\d+(\.\d+)?star$/i)) {
                            const detailName = await fetchAppDetails(appId);
                            if (detailName) {
                                appName = detailName;
                            } else {
                                appName = appId.split('.').pop() || appId;
                            }
                        }

                        // Get other app details
                        const detailUrl = `https://play.google.com/store/apps/details?id=${appId}&hl=en`;
                        const detailResponse = await axios.get(detailUrl, {
                            headers: {
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                                'Accept-Language': 'en-US,en;q=0.9',
                            }
                        });

                        const $detail = cheerio.load(detailResponse.data);

                        // Extract support email and website
                        let supportEmail = '';
                        let developerWebsite = '';

                        // Look for email
                        const emailRegex = /[\w.-]+@[\w.-]+\.\w+/;
                        $detail('a[href^="mailto:"], div.sMUprd, div.reAt0').each((_, el) => {
                            const text = $detail(el).text().trim();
                            const href = $detail(el).attr('href');

                            if (href?.startsWith('mailto:')) {
                                supportEmail = href.replace('mailto:', '');
                                return false;
                            }

                            const emailMatch = text.match(emailRegex);
                            if (emailMatch && !text.includes('privacy')) {
                                supportEmail = emailMatch[0];
                                return false;
                            }
                        });

                        // Look for website
                        $detail('a[href^="http"]').each((_, el) => {
                            const href = $detail(el).attr('href');
                            if (href &&
                                !href.includes('play.google.com') &&
                                !href.includes('google.com') &&
                                !href.includes('privacy') &&
                                !href.includes('policy')) {
                                developerWebsite = href;
                                return false;
                            }
                        });

                        return {
                            appName,
                            developer: developer || 'Unknown Developer',
                            rating,
                            category: category || 'Unknown',
                            appId,
                            appUrl: `https://play.google.com/store/apps/details?id=${appId}`,
                            supportEmail,
                            developerWebsite
                        };
                    }
                    return null;
                } catch (error) {
                    console.error(`Error fetching details for ${appId}:`, error);
                    return null;
                }
            })();

            if (appId && !allApps.some(app => app.appId === appId)) {
                appPromises.push(detailPromise);
            }
        } catch (error) {
            console.error('Error processing app element:', error);
        }
    });

    // Wait for all app details to be fetched
    const appDetails = await Promise.all(appPromises);
    const uniqueApps = new Map();
    appDetails
        .filter(app => app !== null)
        .forEach(app => {
            if (!uniqueApps.has(app.appId)) {
                uniqueApps.set(app.appId, app);
            }
        });
    allApps.push(...Array.from(uniqueApps.values()));
} 