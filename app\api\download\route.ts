import { NextResponse } from 'next/server';
import { formatCategoryName } from '../../constants/categories';
const gplayModule = require('google-play-scraper');
const gplay = gplayModule.default || gplayModule;

interface AppData {
    appName: string;
    developer: string;
    rating: number | null;
    imgSrc: string;
    appId: string;
    appUrl: string;
    installs: number;
    category: string;
}

export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '200');
    const categoryParam = searchParams.get('category') || 'ALL';

    // Fetch maximum possible data from Google Play APIs
    // Google Play Scraper limits: search queries max 250, category browsing max 500
    const fetchLimit = query ? 250 : 500;

    // Get filter parameters
    const minInstalls = parseInt(searchParams.get('minInstalls') || '0');
    const maxInstalls = parseInt(searchParams.get('maxInstalls') || '0');
    const minRating = parseFloat(searchParams.get('minRating') || '0');
    const maxRating = parseFloat(searchParams.get('maxRating') || '5');

    console.log('🚀 MAXIMUM DATA DOWNLOAD - Starting download with params:', { query, page, limit, categoryParam, fetchLimit });

    try {
        let apps: AppData[] = [];

        // Handle search based on query and/or category
        if (query) {
            // Search by query
            const searchResults = await gplay.search({
                term: query,
                num: fetchLimit,
                fullDetail: true,
                price: 'all'
            });

            apps = searchResults.map((app: any) => {
                // Extract actual category from the app data
                const actualCategory = app.genreId || app.genre || app.category || 'UNKNOWN';

                return {
                    appName: app.title,
                    developer: app.developer,
                    rating: app.score || null,
                    imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                    appId: app.appId,
                    appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                    installs: parseInt(app.installs?.replace(/[^0-9]/g, '') || '0'),
                    category: formatCategoryName(actualCategory)
                };
            });
        } else {
            // Fetch by category
            let categoryId;
            if (categoryParam === 'ALL') {
                categoryId = gplay.category.GAME; // Default to games for ALL
            } else {
                // Map our category names to gplay category constants (same as scrape API)
                const categoryMap: { [key: string]: string } = {
                    'GAME': gplay.category.GAME,
                    'SOCIAL': gplay.category.SOCIAL,
                    'COMMUNICATION': gplay.category.COMMUNICATION,
                    'PRODUCTIVITY': gplay.category.PRODUCTIVITY,
                    'EDUCATION': gplay.category.EDUCATION,
                    'ENTERTAINMENT': gplay.category.ENTERTAINMENT,
                    'MUSIC_AND_AUDIO': gplay.category.MUSIC_AND_AUDIO,
                    'VIDEO_PLAYERS': gplay.category.VIDEO_PLAYERS,
                    'PHOTOGRAPHY': gplay.category.PHOTOGRAPHY,
                    'TOOLS': gplay.category.TOOLS,
                    'SHOPPING': gplay.category.SHOPPING,
                    'FINANCE': gplay.category.FINANCE,
                    'HEALTH_AND_FITNESS': gplay.category.HEALTH_AND_FITNESS,
                    'LIFESTYLE': gplay.category.LIFESTYLE,
                    'BOOKS_AND_REFERENCE': gplay.category.BOOKS_AND_REFERENCE,
                    // Additional categories
                    'SPORTS': gplay.category.SPORTS,
                    'MEDICAL': gplay.category.MEDICAL,
                    'MAPS_AND_NAVIGATION': gplay.category.MAPS_AND_NAVIGATION,
                    'AUTO_AND_VEHICLES': gplay.category.AUTO_AND_VEHICLES,
                    'PERSONALIZATION': gplay.category.PERSONALIZATION,
                    'DATING': gplay.category.DATING,
                    'FOOD_AND_DRINK': gplay.category.FOOD_AND_DRINK,
                    'HOUSE_AND_HOME': gplay.category.HOUSE_AND_HOME,
                    // Previously removed categories that are actually available
                    'NEWS_AND_MAGAZINES': gplay.category.NEWS_AND_MAGAZINES,
                    'WEATHER': gplay.category.WEATHER,
                    'TRAVEL_AND_LOCAL': gplay.category.TRAVEL_AND_LOCAL,
                    'BUSINESS': gplay.category.BUSINESS,
                    // Additional Google Play categories
                    'ART_AND_DESIGN': gplay.category.ART_AND_DESIGN,
                    'BEAUTY': gplay.category.BEAUTY,
                    'COMICS': gplay.category.COMICS,
                    'EVENTS': gplay.category.EVENTS,
                    'PARENTING': gplay.category.PARENTING
                };
                categoryId = categoryMap[categoryParam] || gplay.category.GAME;
            }

            console.log('Fetching apps for category:', categoryParam, 'mapped to:', categoryId);

            const categoryApps = await gplay.list({
                category: categoryId,
                collection: gplay.collection.TOP_FREE,
                num: fetchLimit,
                fullDetail: true,
                country: 'us',
                language: 'en'
            });

            apps = categoryApps.map((app: any) => {
                // Extract actual category from the app data
                const actualCategory = app.genreId || app.genre || app.category || categoryParam;

                return {
                    appName: app.title,
                    developer: app.developer,
                    rating: app.score || null,
                    imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                    appId: app.appId,
                    appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                    installs: parseInt(app.installs?.replace(/[^0-9]/g, '') || '0'),
                    category: formatCategoryName(actualCategory)
                };
            });
        }

        // Apply filters
        const filteredApps = apps.filter(app => {
            const installsValid = (!minInstalls || app.installs >= minInstalls) &&
                (!maxInstalls || app.installs <= maxInstalls);
            const ratingValid = (!minRating || (app.rating !== null && app.rating >= minRating)) &&
                (!maxRating || (app.rating !== null && app.rating <= maxRating));

            // Apply category filter if specified and not searching ALL
            // For category filtering, be more lenient since the API already filtered by category
            const categoryValid = categoryParam === 'ALL' ||
                app.category === categoryParam ||
                app.category.includes(categoryParam) ||
                categoryParam.includes('GAME'); // Special case for games

            return installsValid && ratingValid && categoryValid;
        });

        console.log(`Filtered ${apps.length} apps down to ${filteredApps.length} apps`);

        // Apply pagination
        const startIndex = (page - 1) * limit;
        const paginatedApps = filteredApps.slice(startIndex, startIndex + limit);

        // Generate CSV content with BOM for Excel compatibility
        const BOM = '\uFEFF';
        const csvHeader = 'App Name,Developer,Rating,Category,App ID,App URL,Installs\n';
        const csvRows = paginatedApps.map(app => {
            return [
                `"${(app.appName || '').replace(/"/g, '""')}"`,
                `"${(app.developer || 'Unknown Developer').replace(/"/g, '""')}"`,
                app.rating || '',
                `"${(app.category || 'Unknown').replace(/"/g, '""')}"`,
                `"${app.appId}"`,
                `"${app.appUrl}"`,
                app.installs || 0
            ].join(',');
        }).join('\n');

        const csvContent = BOM + csvHeader + csvRows;

        // Set filename based on search type
        const filename = query
            ? `app_search_${query.replace(/[^a-zA-Z0-9]/g, '_')}_page${page}.csv`
            : `${categoryParam.toLowerCase()}_apps_page${page}.csv`;

        // Return CSV file
        return new NextResponse(csvContent, {
            headers: {
                'Content-Type': 'text/csv; charset=utf-8',
                'Content-Disposition': `attachment; filename="${filename}"`,
            },
        });

    } catch (error) {
        console.error('Error generating CSV:', error);
        return NextResponse.json({ error: 'Failed to generate CSV' }, { status: 500 });
    }
}