import { NextResponse } from 'next/server';
const gplayModule = require('google-play-scraper');
const gplay = gplayModule.default || gplayModule;

interface AppData {
    appName: string;
    developer: string;
    rating: number | null;
    imgSrc: string;
    appId: string;
    appUrl: string;
    installs: number;
    category: string;
}

export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '200');
    const categoryParam = searchParams.get('category') || 'ALL';

    // Get fetch limit parameter (how many apps to fetch from Google Play)
    // Note: Google Play Scraper has different limits for search vs category listing
    const requestedFetchLimit = parseInt(searchParams.get('fetchLimit') || '250');
    const fetchLimit = query
        ? Math.min(requestedFetchLimit, 250) // Search queries max 250
        : Math.min(requestedFetchLimit, 500); // Category listing max 500

    // Get filter parameters
    const minInstalls = parseInt(searchParams.get('minInstalls') || '0');
    const maxInstalls = parseInt(searchParams.get('maxInstalls') || '0');
    const minRating = parseFloat(searchParams.get('minRating') || '0');
    const maxRating = parseFloat(searchParams.get('maxRating') || '5');

    console.log('Starting download with params:', { query, page, limit, categoryParam, fetchLimit });

    try {
        let apps: AppData[] = [];

        // Handle search based on query and/or category
        if (query) {
            // Search by query
            const searchResults = await gplay.search({
                term: query,
                num: fetchLimit,
                fullDetail: true,
                price: 'all'
            });

            apps = searchResults.map((app: any) => ({
                appName: app.title,
                developer: app.developer,
                rating: app.score || null,
                imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                appId: app.appId,
                appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                installs: parseInt(app.installs?.replace(/[^0-9]/g, '') || '0'),
                category: app.genreId?.toUpperCase() || 'UNKNOWN'
            }));
        } else {
            // Fetch by category
            let categoryId;
            if (categoryParam === 'ALL') {
                categoryId = gplay.category.GAME; // Default to games for ALL
            } else {
                const categoryMap: { [key: string]: any } = {
                    'GAME': gplay.category.GAME,
                    'SOCIAL': gplay.category.SOCIAL,
                    'PRODUCTIVITY': gplay.category.PRODUCTIVITY,
                    'EDUCATION': gplay.category.EDUCATION,
                    'ENTERTAINMENT': gplay.category.ENTERTAINMENT,
                    'COMMUNICATION': gplay.category.COMMUNICATION,
                    'TOOLS': gplay.category.TOOLS,
                    'SHOPPING': gplay.category.SHOPPING,
                    'HEALTH_AND_FITNESS': gplay.category.HEALTH_AND_FITNESS,
                    'LIFESTYLE': gplay.category.LIFESTYLE,
                    'BOOKS_AND_REFERENCE': gplay.category.BOOKS_AND_REFERENCE
                };
                categoryId = categoryMap[categoryParam] || gplay.category.GAME;
            }

            console.log('Fetching apps for category:', categoryId);

            const categoryApps = await gplay.list({
                category: categoryId,
                collection: gplay.collection.TOP_FREE,
                num: fetchLimit,
                fullDetail: true,
                country: 'us',
                language: 'en'
            });

            apps = categoryApps.map((app: any) => ({
                appName: app.title,
                developer: app.developer,
                rating: app.score || null,
                imgSrc: `/api/proxy?url=${encodeURIComponent(app.icon)}`,
                appId: app.appId,
                appUrl: `https://play.google.com/store/apps/details?id=${app.appId}`,
                installs: parseInt(app.installs?.replace(/[^0-9]/g, '') || '0'),
                category: app.genreId?.toUpperCase() || categoryParam
            }));
        }

        // Apply filters
        const filteredApps = apps.filter(app => {
            const installsValid = (!minInstalls || app.installs >= minInstalls) &&
                (!maxInstalls || app.installs <= maxInstalls);
            const ratingValid = (!minRating || (app.rating !== null && app.rating >= minRating)) &&
                (!maxRating || (app.rating !== null && app.rating <= maxRating));

            // Apply category filter if specified and not searching ALL
            // For category filtering, be more lenient since the API already filtered by category
            const categoryValid = categoryParam === 'ALL' ||
                app.category === categoryParam ||
                app.category.includes(categoryParam) ||
                categoryParam.includes('GAME'); // Special case for games

            return installsValid && ratingValid && categoryValid;
        });

        console.log(`Filtered ${apps.length} apps down to ${filteredApps.length} apps`);

        // Apply pagination
        const startIndex = (page - 1) * limit;
        const paginatedApps = filteredApps.slice(startIndex, startIndex + limit);

        // Generate CSV content with BOM for Excel compatibility
        const BOM = '\uFEFF';
        const csvHeader = 'App Name,Developer,Rating,Category,App ID,App URL,Installs\n';
        const csvRows = paginatedApps.map(app => {
            return [
                `"${(app.appName || '').replace(/"/g, '""')}"`,
                `"${(app.developer || 'Unknown Developer').replace(/"/g, '""')}"`,
                app.rating || '',
                `"${(app.category || 'Unknown').replace(/"/g, '""')}"`,
                `"${app.appId}"`,
                `"${app.appUrl}"`,
                app.installs || 0
            ].join(',');
        }).join('\n');

        const csvContent = BOM + csvHeader + csvRows;

        // Set filename based on search type
        const filename = query
            ? `app_search_${query.replace(/[^a-zA-Z0-9]/g, '_')}_page${page}.csv`
            : `${categoryParam.toLowerCase()}_apps_page${page}.csv`;

        // Return CSV file
        return new NextResponse(csvContent, {
            headers: {
                'Content-Type': 'text/csv; charset=utf-8',
                'Content-Disposition': `attachment; filename="${filename}"`,
            },
        });

    } catch (error) {
        console.error('Error generating CSV:', error);
        return NextResponse.json({ error: 'Failed to generate CSV' }, { status: 500 });
    }
}
// Extract app data using various selectors
const appPromises: Promise<AppData | null>[] = [];
$('div[data-uitype="500"], c-wiz[data-node-index] div.VfPpkd-aGsRMb, div.ULeU3b, div.Vpfmgd').each((_, element) => {
    try {
        const el = $(element);

        // Helper function to fetch app details
        async function fetchAppDetails(appId: string): Promise<string | null> {
            try {
                const detailUrl = `https://play.google.com/store/apps/details?id=${appId}&hl=en`;
                const detailResponse = await axios.get(detailUrl, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept-Language': 'en-US,en;q=0.9',
                    }
                });

                const $detail = cheerio.load(detailResponse.data);
                const detailNameSelectors = [
                    'h1[itemprop="name"]',
                    'h1.AHFaub',
                    'h1.Fd93Bb',
                    'div.ooJXfd span',
                    'div.Vbfug span'
                ];

                for (const selector of detailNameSelectors) {
                    const detailName = $detail(selector).first().text().trim();
                    if (detailName &&
                        detailName.length > 1 &&
                        !detailName.match(/^\d+(\.\d+)?star$/i) &&
                        !detailName.match(/^[\.]+$/)) {
                        return detailName;
                    }
                }
            } catch (error) {
                console.error(`Error fetching app details for name: ${error}`);
            }
            return null;
        }

        // Enhanced app name extraction with better validation
        let appName = '';
        let developer = '';
        let rating: number | null = null;
        let category = '';
        let appId = '';

        // First extract app ID to help validate app name
        const appLink = el.find('a[href*="details?id="]').attr('href') || '';
        const appIdMatch = appLink.match(/id=([^&]+)/);
        if (appIdMatch) {
            appId = appIdMatch[1];
        }

        // Extract app name with strict validation
        const nameSelectors = [
            'div.WsMG1c',
            'div.b8cIId span',
            'span.DdYX5',
            'div.vWM94c',
            'span.sT93qe',
            'div.poRVub',
            'a[href*="details?id="] span',
            'a[href*="details?id="] div',
            'h1[itemprop="name"]',
            'h1.AHFaub span',
            'h1.Fd93Bb span',
            'div.ooJXfd span',
            'div.Vbfug span',
            'div.qZmL0 div[role="heading"]',
            'div[role="heading"]',
            'div.xwY9Zc',
            'div.Vbfug > div:first-child',
            'div.b8cIId > div:first-child'
        ];

        // First pass: Try to find app name with strict validation
        for (const selector of nameSelectors) {
            const nameElements = el.find(selector);
            for (let i = 0; i < nameElements.length; i++) {
                const item = nameElements[i];
                const nameText = $(item).text().trim();
                // Strict validation to avoid false positives
                if (nameText &&
                    nameText.length > 1 &&
                    nameText.length < 100 &&
                    !nameText.match(/^\d+(\.\d+)?$/) &&
                    !nameText.match(/^\d+(\.\d+)?star$/i) &&
                    !nameText.match(/^(free|paid|top|new|trending)$/i) &&
                    !nameText.match(/^(install|download|update|open)$/i) &&
                    !nameText.match(/^[\.]+$/) &&  // Prevent dots
                    !nameText.includes('rating') &&
                    !nameText.includes('reviews') &&
                    !nameText.includes('stars') &&
                    nameText !== '.' &&
                    nameText !== '..' &&
                    nameText !== '...') {
                    appName = nameText;
                    break;
                }
            }
            if (appName) break;
        }

        // Extract rating with strict validation
        const ratingSelectors = [
            '[aria-label*="rating"]',
            '[aria-label*="Rated"]',
            '.TT9eCd',
            '.jILTFe'
        ];

        for (const selector of ratingSelectors) {
            const ratingEl = el.find(selector).first();
            const ratingText = ratingEl.text().trim();
            const ratingMatch = ratingText.match(/(\d+(\.\d+)?)/);
            if (ratingMatch) {
                const parsedRating = parseFloat(ratingMatch[1]);
                if (!isNaN(parsedRating) && parsedRating >= 0 && parsedRating <= 5) {
                    rating = parsedRating;
                    break;
                }
            }
        }

        // Extract category with strict validation
        const categorySelectors = [
            'a[href*="/store/apps/category/"]',
            'span[itemprop="genre"]',
            'div[itemprop="genre"]',
            'a[href*="browse/"]'
        ];

        for (const selector of categorySelectors) {
            const categoryEl = el.find(selector).first();
            const categoryText = categoryEl.text().trim();
            if (categoryText &&
                !categoryText.match(/^\d+(\.\d+)?star$/i) &&
                !categoryText.match(/^\d+(\.\d+)?$/) &&
                !categoryText.includes('rating') &&
                !categoryText.includes('reviews') &&
                categoryText !== appName) {
                category = categoryText;
                break;
            }
        }

        // Extract developer info with validation
        const developerSelectors = [
            'a[href*="developer/"]',
            'div[itemprop="author"]',
            '.b8cIId a',
            '.KoLSrc',
            '.pdqJpd'
        ];

        for (const selector of developerSelectors) {
            const devEl = el.find(selector).first();
            const devText = devEl.text().trim();
            if (devText &&
                devText !== appName &&
                devText !== category &&
                !devText.match(/^\d+(\.\d+)?star$/i) &&
                !devText.match(/^\d+(\.\d+)?$/)) {
                developer = devText;
                break;
            }
        }

        // If no valid app name found, try to extract from app ID
        if (!appName && appId) {
            appName = appId.split('.').pop() || appId;
        }

        // Create a promise for fetching detailed app info
        const detailPromise = (async (): Promise<AppData | null> => {
            try {
                if (appId) {
                    // If no valid name found, try to get it from app details
                    if (!appName || appName === '.' || appName.match(/^\d+(\.\d+)?star$/i)) {
                        const detailName = await fetchAppDetails(appId);
                        if (detailName) {
                            appName = detailName;
                        } else {
                            appName = appId.split('.').pop() || appId;
                        }
                    }

                    // Get other app details
                    const detailUrl = `https://play.google.com/store/apps/details?id=${appId}&hl=en`;
                    const detailResponse = await axios.get(detailUrl, {
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                            'Accept-Language': 'en-US,en;q=0.9',
                        }
                    });

                    const $detail = cheerio.load(detailResponse.data);

                    // Extract support email and website
                    let supportEmail = '';
                    let developerWebsite = '';

                    // Look for email
                    const emailRegex = /[\w.-]+@[\w.-]+\.\w+/;
                    $detail('a[href^="mailto:"], div.sMUprd, div.reAt0').each((_, el) => {
                        const text = $detail(el).text().trim();
                        const href = $detail(el).attr('href');

                        if (href?.startsWith('mailto:')) {
                            supportEmail = href.replace('mailto:', '');
                            return false;
                        }

                        const emailMatch = text.match(emailRegex);
                        if (emailMatch && !text.includes('privacy')) {
                            supportEmail = emailMatch[0];
                            return false;
                        }
                    });

                    // Look for website
                    $detail('a[href^="http"]').each((_, el) => {
                        const href = $detail(el).attr('href');
                        if (href &&
                            !href.includes('play.google.com') &&
                            !href.includes('google.com') &&
                            !href.includes('privacy') &&
                            !href.includes('policy')) {
                            developerWebsite = href;
                            return false;
                        }
                    });

                    return {
                        appName,
                        developer: developer || 'Unknown Developer',
                        rating,
                        category: category || 'Unknown',
                        appId,
                        appUrl: `https://play.google.com/store/apps/details?id=${appId}`,
                        supportEmail,
                        developerWebsite
                    };
                }
                return null;
            } catch (error) {
                console.error(`Error fetching details for ${appId}:`, error);
                return null;
            }
        })();

        if (appId && !allApps.some(app => app.appId === appId)) {
            appPromises.push(detailPromise);
        }
    } catch (error) {
        console.error('Error processing app element:', error);
    }
});

// Wait for all app details to be fetched
const appDetails = await Promise.all(appPromises);
const uniqueApps = new Map();
appDetails
    .filter(app => app !== null)
    .forEach(app => {
        if (!uniqueApps.has(app.appId)) {
            uniqueApps.set(app.appId, app);
        }
    });
allApps.push(...Array.from(uniqueApps.values()));
}